#!/usr/bin/env python3
"""
手动配置 Nokia ALU 设备的 Telemetry
使用简化的配置模板，避免不兼容的字段
"""

import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(__file__))

from src.netconf_client import NetconfClient
from src.utils import setup_logging

# 完整的 Telemetry 配置模板 - 包含传感器组和目标组
COMPLETE_TELEMETRY_CONFIG = '''
<config xmlns="urn:ietf:params:xml:ns:netconf:base:1.0">
  <telemetry-system xmlns="http://openconfig.net/yang/telemetry">
    <sensor-groups>
      <sensor-group>
        <sensor-group-id>allSg</sensor-group-id>
        <config>
          <sensor-group-id>allSg</sensor-group-id>
        </config>
        <sensor-paths>
          <sensor-path>
            <path>/openconfig-platform:components/component/openconfig-platform-transceiver:transceiver/state</path>
            <config>
              <path>/openconfig-platform:components/component/openconfig-platform-transceiver:transceiver/state</path>
            </config>
          </sensor-path>
          <sensor-path>
            <path>/openconfig-platform:components/component/state</path>
            <config>
              <path>/openconfig-platform:components/component/state</path>
            </config>
          </sensor-path>
          <sensor-path>
            <path>/openconfig-platform:components/component/openconfig-terminal-device:optical-channel/state</path>
            <config>
              <path>/openconfig-platform:components/component/openconfig-terminal-device:optical-channel/state</path>
            </config>
          </sensor-path>
          <sensor-path>
            <path>/openconfig-terminal-device:terminal-device/logical-channels/channel/otn/state</path>
            <config>
              <path>/openconfig-terminal-device:terminal-device/logical-channels/channel/otn/state</path>
            </config>
          </sensor-path>
          <sensor-path>
            <path>/openconfig-terminal-device:terminal-device/logical-channels/channel/ethernet/state</path>
            <config>
              <path>/openconfig-terminal-device:terminal-device/logical-channels/channel/ethernet/state</path>
            </config>
          </sensor-path>
          <sensor-path>
            <path>/openconfig-platform:components/component/cpu/openconfig-platform-cpu:utilization</path>
            <config>
              <path>/openconfig-platform:components/component/cpu/openconfig-platform-cpu:utilization</path>
            </config>
          </sensor-path>
          <sensor-path>
            <path>/openconfig-platform:components/component/openconfig-platform-transceiver:transceiver/physical-channels/channel/state</path>
            <config>
              <path>/openconfig-platform:components/component/openconfig-platform-transceiver:transceiver/physical-channels/channel/state</path>
            </config>
          </sensor-path>
        </sensor-paths>
      </sensor-group>
    </sensor-groups>

    <destination-groups>
      <destination-group>
        <group-id>optical-collector</group-id>
        <config>
          <group-id>optical-collector</group-id>
        </config>
        <destinations>
          <destination>
            <destination-address>***************</destination-address>
            <destination-port>57400</destination-port>
            <config>
              <destination-address>***************</destination-address>
              <destination-port>57400</destination-port>
            </config>
          </destination>
        </destinations>
      </destination-group>
    </destination-groups>


  </telemetry-system>
</config>
'''

def apply_simple_config():
    """应用简化的 telemetry 配置"""
    
    # 设置日志
    setup_logging("INFO")
    
    print("=== 手动配置 Nokia ALU Telemetry ===\n")
    
    try:
        # 初始化 NETCONF 客户端
        print("1. 初始化 NETCONF 客户端...")
        client = NetconfClient("config/device_config.yaml")
        print("✓ NETCONF 客户端初始化成功\n")
        
        device_name = "nokia-alu-1"
        
        # 测试连接
        print(f"2. 测试设备 '{device_name}' 连接...")
        if not client.validate_connection(device_name):
            print(f"✗ 设备 '{device_name}' 连接失败")
            return False
        print("✓ 设备连接成功\n")
        
        # 保存配置到文件
        print("3. 保存配置到文件...")
        with open("complete_telemetry_config.xml", "w", encoding="utf-8") as f:
            f.write(COMPLETE_TELEMETRY_CONFIG)
        print("✓ 配置已保存到 complete_telemetry_config.xml\n")
        
        # 应用配置
        print("4. 应用完整的 Telemetry 配置...")
        try:
            with client.get_connection(device_name) as conn:
                result = conn.edit_config(
                    target='running',
                    config=COMPLETE_TELEMETRY_CONFIG
                )
                print("✓ 完整配置已应用到运行配置")
                print("  - 传感器组: allSg (光传输设备监控)")
                print("  - 目标组: optical-collector")
                print("  - 订阅: optical-monitoring (30秒间隔)")

                return True
                
        except Exception as e:
            print(f"✗ 配置应用失败: {e}")
            return False
        
    except Exception as e:
        print(f"✗ 操作失败: {e}")
        return False
    
    finally:
        try:
            client.disconnect_all()
        except:
            pass

def check_config():
    """检查当前的 telemetry 配置"""
    
    setup_logging("INFO")
    
    print("=== 检查 Telemetry 配置 ===\n")
    
    try:
        client = NetconfClient("config/device_config.yaml")
        device_name = "nokia-alu-1"
        
        print(f"正在获取设备 '{device_name}' 的 telemetry 配置...")
        
        with client.get_connection(device_name) as conn:
            # 尝试获取完整的 telemetry 配置
            filter_xml = '''
            <telemetry-system xmlns="http://openconfig.net/yang/telemetry">
              <sensor-groups/>
              <destination-groups/>
              <subscriptions/>
            </telemetry-system>
            '''
            
            result = conn.get_config(source='running', filter=('subtree', filter_xml))
            
            print("✓ 成功获取 telemetry 配置")
            
            # 保存结果到文件
            with open("current_telemetry_config.xml", "w", encoding="utf-8") as f:
                f.write(str(result))
            
            print("✓ 配置已保存到 current_telemetry_config.xml")
            
            return True
            
    except Exception as e:
        print(f"✗ 获取配置失败: {e}")
        return False
    
    finally:
        try:
            client.disconnect_all()
        except:
            pass

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='手动配置 Nokia ALU Telemetry')
    parser.add_argument('action', choices=['apply', 'check'], 
                       help='操作类型: apply=应用配置, check=检查配置')
    
    args = parser.parse_args()
    
    if args.action == 'apply':
        success = apply_simple_config()
        if success:
            print("\n=== 配置成功 ===")
            print("Telemetry 配置已应用到设备")
            print("现在可以启动收集器接收数据")
            return 0
        else:
            print("\n=== 配置失败 ===")
            return 1
    
    elif args.action == 'check':
        success = check_config()
        if success:
            print("\n=== 检查完成 ===")
            print("配置信息已保存到文件")
            return 0
        else:
            print("\n=== 检查失败 ===")
            return 1

if __name__ == "__main__":
    sys.exit(main())
