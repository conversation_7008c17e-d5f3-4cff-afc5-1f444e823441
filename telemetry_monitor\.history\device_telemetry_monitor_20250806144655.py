import grpc
import json
import threading
from datetime import datetime

# Define a function to handle telemetry data for a single device
def monitor_device(device_address, device_port, output_file):
    """
    Connects to the device's telemetry stream and writes data to a JSON file.

    :param device_address: IP address of the device
    :param device_port: Port for the telemetry stream
    :param output_file: Path to the output JSON file
    """
    try:
        # Create a gRPC channel
        channel = grpc.insecure_channel(f"{device_address}:{device_port}")

        # Stub and telemetry subscription logic would go here
        # For demonstration, we'll simulate receiving telemetry data

        while True:
            # Simulate receiving telemetry data (replace with actual gRPC call)
            telemetry_data = {
                "timestamp": datetime.utcnow().isoformat(),
                "device": device_address,
                "data": {
                    "example_metric": 123.45  # Replace with actual telemetry data
                }
            }

            # Write telemetry data to the JSON file
            with open(output_file, "a") as f:
                f.write(json.dumps(telemetry_data) + "\n")

    except Exception as e:
        print(f"Error monitoring device {device_address}: {e}")

# List of devices to monitor (replace with actual device details)
devices = [
    {"address": "***************", "port": 50051, "output_file": "device_***************.json"},
    # Add more devices as needed
]

# Create and start a thread for each device
threads = []
for device in devices:
    t = threading.Thread(
        target=monitor_device,
        args=(device["address"], device["port"], device["output_file"])
    )
    threads.append(t)
    t.start()

# Wait for all threads to complete
for t in threads:
    t.join()
