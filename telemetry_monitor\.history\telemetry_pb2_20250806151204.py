# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: telemetry.proto
# Protobuf Python Version: 6.31.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    1,
    '',
    'telemetry.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0ftelemetry.proto\x12\ttelemetry\"I\n\x1cTelemetrySubscriptionRequest\x12\x17\n\x0fsubscription_id\x18\x01 \x01(\t\x12\x10\n\x08\x65ncoding\x18\x02 \x01(\t\"w\n\x15TelemetryDataResponse\x12\x0f\n\x07node_id\x18\x01 \x01(\t\x12\x17\n\x0fsubscription_id\x18\x02 \x01(\t\x12\x13\n\x0bsensor_path\x18\x03 \x01(\t\x12\x11\n\ttimestamp\x18\x04 \x01(\t\x12\x0c\n\x04\x64\x61ta\x18\x05 \x01(\x0c\x32t\n\x0fgRPCDataservice\x12\x61\n\x12telemetrySubscribe\x12\'.telemetry.TelemetrySubscriptionRequest\x1a .telemetry.TelemetryDataResponse0\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'telemetry_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  DESCRIPTOR._loaded_options = None
  _globals['_TELEMETRYSUBSCRIPTIONREQUEST']._serialized_start=30
  _globals['_TELEMETRYSUBSCRIPTIONREQUEST']._serialized_end=103
  _globals['_TELEMETRYDATARESPONSE']._serialized_start=105
  _globals['_TELEMETRYDATARESPONSE']._serialized_end=224
  _globals['_GRPCDATASERVICE']._serialized_start=226
  _globals['_GRPCDATASERVICE']._serialized_end=342
# @@protoc_insertion_point(module_scope)
