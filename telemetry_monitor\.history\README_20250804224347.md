# OpenConfig Telemetry Monitor

基于 ncclient 和 OpenConfig YANG 模型的网络设备 Telemetry 配置工具。

## 功能特性

✅ **完整的 NETCONF 支持**
- 支持用户名/密码和 SSH 密钥认证
- 自动重连和连接池管理
- 完善的错误处理和超时控制

✅ **全面的监控指标**
- **系统监控**: CPU 利用率、内存使用率、存储空间、系统温度
- **接口监控**: 流量统计、接口状态、错误统计、光模块信息
- **路由协议**: BGP 邻居、OSPF 邻居、ISIS 邻接、路由表统计
- **硬件监控**: 风扇状态、电源状态、温度传感器、硬件告警
- **QoS 监控**: 队列统计、策略匹配、流分类、带宽利用率
- **MPLS/VPN**: LSP 状态、VRF 统计、MPLS 标签、VPN 连接

✅ **灵活的配置方式**
- 预设监控模板（基础、高级、企业级、性能）
- 自定义监控类别组合
- 可配置的收集器地址和端口
- 灵活的采样间隔设置

✅ **标准兼容**
- 完全基于 OpenConfig Telemetry YANG 模型
- 标准 NETCONF 协议通信
- 支持 gRPC 和 JSON 编码

## 安装

### 环境要求

- Python 3.8+
- 支持 OpenConfig Telemetry 的网络设备

### 安装步骤

1. 克隆项目
```bash
git clone https://github.com/example/openconfig-telemetry-monitor.git
cd openconfig-telemetry-monitor
```

2. 安装依赖
```bash
pip install -r requirements.txt
```

3. 或使用 setup.py 安装
```bash
python setup.py install
```

## 快速开始

### 1. 配置设备连接

编辑 `config/device_config.yaml`：

```yaml
devices:
  - name: "router-1"
    host: "***********"
    port: 830
    username: "admin"
    password: "password"
    timeout: 30
    device_type: "generic"
```

### 2. 配置 Telemetry 参数

编辑 `config/telemetry_config.yaml`：

```yaml
collectors:
  - name: "default-collector"
    address: "*************"
    port: 57400
    protocol: "grpc"
    encoding: "json"
```

### 3. 测试设备连接

```bash
python main.py test-connection router-1
```

### 4. 配置基础监控

```bash
python main.py configure router-1 --template basic --collector *************:57400
```

## 使用指南

### 命令行接口

#### 查看帮助
```bash
python main.py --help
```

#### 列出设备
```bash
python main.py list-devices
```

#### 查看可用模板
```bash
python main.py list-templates
```

#### 查看监控类别
```bash
python main.py list-categories
```

#### 配置设备监控
```bash
# 使用预设模板
python main.py configure router-1 --template enterprise --collector *************:57400

# 自定义监控类别
python main.py configure-custom router-1 \
  --categories "system_all,interface_all,routing_protocols" \
  --collector *************:57400 \
  --interval 15000
```

#### 批量配置
```bash
# 配置所有设备
python main.py batch-configure --template advanced --collector *************:57400

# 配置指定设备
python main.py batch-configure --devices "router-1,router-2" \
  --template enterprise --collector *************:57400
```

#### 查看状态
```bash
python main.py status router-1
```

#### 验证配置
```bash
python main.py validate router-1
```

### Python API

```python
from src.telemetry_config import TelemetryConfigurator

# 初始化配置器
configurator = TelemetryConfigurator(
    device_config_file="config/device_config.yaml",
    telemetry_config_file="config/telemetry_config.yaml"
)

# 添加收集器
configurator.add_collector("my-collector", "*************", 57400)

# 配置设备监控
success = configurator.configure_telemetry(
    device_name="router-1",
    template_name="enterprise",
    collector_name="my-collector",
    sample_interval=10000
)

# 自定义配置
success = configurator.configure_custom_telemetry(
    device_name="router-1",
    categories=["system_all", "interface_all", "routing_protocols"],
    collector_address="*************",
    collector_port=57400,
    sample_interval=15000
)

# 获取状态
status = configurator.get_telemetry_status("router-1")
print(status)

# 验证配置
results = configurator.validate_configuration("router-1")
print(results)

# 清理资源
configurator.cleanup()
```

## 监控模板

### 基础模板 (basic)
- **描述**: 基础系统和接口监控
- **类别**: system_basic, interface_basic
- **采样间隔**: 30秒
- **适用场景**: 基本的设备健康监控

### 高级模板 (advanced)
- **描述**: 包含路由协议的高级监控
- **类别**: system_all, interface_all, routing_protocols
- **采样间隔**: 15秒
- **适用场景**: 网络运维和故障排查

### 企业级模板 (enterprise)
- **描述**: 全面的企业级监控
- **类别**: 所有监控类别
- **采样间隔**: 10秒
- **适用场景**: 大型企业网络的全面监控

### 性能模板 (performance)
- **描述**: 专注于性能指标的监控
- **类别**: system_performance, interface_performance, qos_monitoring
- **采样间隔**: 5秒
- **适用场景**: 性能分析和优化

## 监控指标详解

### 系统监控
- **CPU 利用率**: 总体和各核心 CPU 使用率
- **内存监控**: 物理内存、虚拟内存、缓存使用情况
- **存储监控**: 磁盘使用率、I/O 统计、文件系统状态
- **温度监控**: CPU 温度、主板温度、环境温度

### 接口监控
- **流量统计**: 入/出字节数、包数、速率
- **接口状态**: 管理状态、操作状态、链路状态
- **错误统计**: CRC 错误、丢包、冲突
- **光模块信息**: 光功率、温度、电压、电流

### 路由协议监控
- **BGP 监控**: 邻居状态、路由数量、会话统计
- **OSPF 监控**: 邻居状态、LSA 统计、区域信息
- **ISIS 监控**: 邻接状态、LSP 统计、拓扑信息

### 硬件监控
- **风扇监控**: 转速、状态、告警
- **电源监控**: 电压、电流、功率、状态
- **温度传感器**: 各组件温度、告警阈值

### QoS 监控
- **队列统计**: 队列深度、丢包率、延迟
- **策略匹配**: 匹配包数、字节数、动作统计
- **流分类**: 流量分类统计、标记统计

### MPLS/VPN 监控
- **LSP 监控**: 路径状态、流量统计、切换事件
- **VRF 监控**: 路由数量、接口绑定、流量统计
- **MPLS 标签**: 标签分配、交换统计

## 配置文件详解

### 设备配置文件 (device_config.yaml)

```yaml
devices:
  - name: "设备名称"
    host: "设备IP地址"
    port: 830                    # NETCONF 端口
    username: "用户名"
    password: "密码"             # 可选，与 ssh_key_file 二选一
    ssh_key_file: "/path/to/key" # 可选，SSH 私钥文件
    timeout: 30                  # 连接超时时间
    device_type: "generic"       # 设备类型

connection_settings:
  default_timeout: 30            # 默认超时时间
  retry_count: 3                 # 重试次数
  retry_interval: 5              # 重试间隔
  verify_ssl: false              # 是否验证 SSL 证书
  session_keepalive: 60          # 会话保持时间
```

### Telemetry 配置文件 (telemetry_config.yaml)

```yaml
collectors:
  - name: "收集器名称"
    address: "收集器IP地址"
    port: 57400                  # 收集器端口
    protocol: "grpc"             # 协议类型
    encoding: "json"             # 编码格式

monitoring_templates:
  custom_template:
    description: "自定义模板描述"
    categories:
      - "system_all"
      - "interface_all"
    sample_interval: 20000       # 采样间隔（毫秒）

monitoring_categories:
  custom_category:
    - "cpu_utilization"
    - "memory_utilization"
    - "interface_traffic"
```

## 故障排查

### 常见问题

#### 1. 连接失败
```bash
# 检查网络连通性
ping ***********

# 检查 NETCONF 端口
telnet *********** 830

# 验证认证信息
python main.py test-connection router-1
```

#### 2. 设备不支持 Telemetry
```bash
# 检查设备能力
python main.py test-connection router-1

# 验证配置
python main.py validate router-1
```

#### 3. 配置失败
```bash
# 查看详细日志
python main.py --log-level DEBUG configure router-1

# 使用 dry-run 模式预览配置
python main.py configure router-1 --dry-run
```

### 日志配置

```bash
# 设置日志级别
python main.py --log-level DEBUG

# 输出到文件
python main.py --log-file /var/log/telemetry-monitor.log
```

### 调试模式

```python
import logging
from src.utils import setup_logging

# 启用调试日志
setup_logging("DEBUG", "debug.log")

# 查看详细的 NETCONF 交互
logging.getLogger('ncclient').setLevel(logging.DEBUG)
```

## 最佳实践

### 1. 监控策略
- **基础监控**: 所有设备都应配置基础的系统和接口监控
- **分层监控**: 根据设备重要性选择不同的监控模板
- **采样间隔**: 平衡监控精度和网络负载

### 2. 收集器配置
- **高可用**: 配置多个收集器实现冗余
- **负载均衡**: 将设备分配到不同的收集器
- **网络规划**: 确保收集器网络可达性

### 3. 性能优化
- **批量配置**: 使用批量配置命令提高效率
- **并行处理**: 对于大量设备，考虑并行配置
- **监控优化**: 根据实际需求调整监控指标

### 4. 安全考虑
- **认证**: 使用 SSH 密钥认证替代密码
- **权限**: 为 Telemetry 配置创建专用用户
- **网络**: 在管理网络中部署收集器

## 扩展开发

### 添加新的监控指标

1. 在 `src/yang_templates.py` 中添加新的传感器路径
2. 在配置文件中定义新的监控类别
3. 测试新指标的有效性

### 支持新的设备类型

1. 在 `src/netconf_client.py` 中添加设备特定的处理逻辑
2. 创建设备特定的 YANG 路径映射
3. 添加设备类型验证

### 集成外部系统

```python
# 示例：集成到监控系统
from src.telemetry_config import TelemetryConfigurator

class MonitoringIntegration:
    def __init__(self):
        self.configurator = TelemetryConfigurator()
    
    def auto_configure_device(self, device_info):
        # 根据设备类型自动选择模板
        template = self.select_template(device_info)
        
        # 配置 Telemetry
        return self.configurator.configure_telemetry(
            device_name=device_info['name'],
            template_name=template,
            collector_name="monitoring-system"
        )
```

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request！

## 支持

如有问题，请通过以下方式联系：
- GitHub Issues: https://github.com/example/openconfig-telemetry-monitor/issues
- Email: <EMAIL>