# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: tmp_proto/gnmi.proto
# Protobuf Python Version: 6.31.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    1,
    '',
    'tmp_proto/gnmi.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import any_pb2 as google_dot_protobuf_dot_any__pb2
from google.protobuf import descriptor_pb2 as google_dot_protobuf_dot_descriptor__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x14tmp_proto/gnmi.proto\x12\x04gnmi\x1a\x19google/protobuf/any.proto\x1a google/protobuf/descriptor.proto\"\x96\x01\n\x0cNotification\x12\x11\n\ttimestamp\x18\x01 \x01(\x03\x12\x1a\n\x06prefix\x18\x02 \x01(\x0b\x32\n.gnmi.Path\x12\r\n\x05\x61lias\x18\x03 \x01(\t\x12\x1c\n\x06update\x18\x04 \x03(\x0b\x32\x0c.gnmi.Update\x12\x1a\n\x06\x64\x65lete\x18\x05 \x03(\x0b\x32\n.gnmi.Path\x12\x0e\n\x06\x61tomic\x18\x06 \x01(\x08\"R\n\x06Update\x12\x18\n\x04path\x18\x01 \x01(\x0b\x32\n.gnmi.Path\x12\x1a\n\x05value\x18\x02 \x01(\x0b\x32\x0b.gnmi.Value\x12\x12\n\nduplicates\x18\x03 \x01(\r\"\xe5\x02\n\nTypedValue\x12\x14\n\nstring_val\x18\x01 \x01(\tH\x00\x12\x11\n\x07int_val\x18\x02 \x01(\x03H\x00\x12\x12\n\x08uint_val\x18\x03 \x01(\x04H\x00\x12\x12\n\x08\x62ool_val\x18\x04 \x01(\x08H\x00\x12\x13\n\tbytes_val\x18\x05 \x01(\x0cH\x00\x12\x13\n\tfloat_val\x18\x06 \x01(\x02H\x00\x12&\n\x0b\x64\x65\x63imal_val\x18\x07 \x01(\x0b\x32\x0f.gnmi.Decimal64H\x00\x12)\n\x0cleaflist_val\x18\x08 \x01(\x0b\x32\x11.gnmi.LeaflistValH\x00\x12\'\n\x07\x61ny_val\x18\t \x01(\x0b\x32\x14.google.protobuf.AnyH\x00\x12\x12\n\x08json_val\x18\n \x01(\x0cH\x00\x12\x17\n\rjson_ietf_val\x18\x0b \x01(\x0cH\x00\x12\x13\n\tascii_val\x18\x0c \x01(\tH\x00\x12\x15\n\x0bproto_bytes\x18\r \x01(\x0cH\x00\x42\x07\n\x05value\"Y\n\x04Path\x12\x13\n\x07\x65lement\x18\x01 \x03(\tB\x02\x18\x01\x12\x0e\n\x06origin\x18\x02 \x01(\t\x12\x1c\n\x04\x65lem\x18\x03 \x03(\x0b\x32\x0e.gnmi.PathElem\x12\x0e\n\x06target\x18\x04 \x01(\t\"j\n\x08PathElem\x12\x0c\n\x04name\x18\x01 \x01(\t\x12$\n\x03key\x18\x02 \x03(\x0b\x32\x17.gnmi.PathElem.KeyEntry\x1a*\n\x08KeyEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"4\n\x05Value\x12\r\n\x05value\x18\x01 \x01(\x0c\x12\x1c\n\x04type\x18\x02 \x01(\x0e\x32\x0e.gnmi.Encoding\"J\n\x05\x45rror\x12\x0c\n\x04\x63ode\x18\x01 \x01(\r\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\"\n\x04\x64\x61ta\x18\x03 \x01(\x0b\x32\x14.google.protobuf.Any\".\n\tDecimal64\x12\x0e\n\x06\x64igits\x18\x01 \x01(\x03\x12\x11\n\tprecision\x18\x02 \x01(\r\"0\n\x0bScalarArray\x12!\n\x07\x65lement\x18\x01 \x03(\x0b\x32\x10.gnmi.TypedValue\"|\n\x11SubscribeResponse\x12$\n\x06update\x18\x01 \x01(\x0b\x32\x12.gnmi.NotificationH\x00\x12\x17\n\rsync_response\x18\x03 \x01(\x08H\x00\x12\x1c\n\x05\x65rror\x18\x04 \x01(\x0b\x32\x0b.gnmi.ErrorH\x00\x42\n\n\x08response\"\x91\x01\n\x10SubscribeRequest\x12+\n\tsubscribe\x18\x01 \x01(\x0b\x32\x16.gnmi.SubscriptionListH\x00\x12!\n\x04poll\x18\x02 \x01(\x0b\x32\x11.gnmi.PollRequestH\x00\x12\"\n\x07\x61liases\x18\x03 \x01(\x0b\x32\x0f.gnmi.AliasListH\x00\x42\t\n\x07request\"\xeb\x01\n\x10SubscriptionList\x12(\n\x0csubscription\x18\x01 \x03(\x0b\x32\x12.gnmi.Subscription\x12\x12\n\nuse_models\x18\x02 \x01(\x08\x12 \n\x08\x65ncoding\x18\x03 \x01(\x0e\x32\x0e.gnmi.Encoding\x12!\n\x04mode\x18\x04 \x01(\x0e\x32\x13.gnmi.SubscribeMode\x12\x19\n\x11\x61llow_aggregation\x18\x05 \x01(\x08\x12\x13\n\x0buse_aliases\x18\x06 \x01(\x08\x12\x14\n\x0cupdates_only\x18\x07 \x01(\x08\x12\x0e\n\x06prefix\x18\x08 \x01(\t\"\xbf\x01\n\x0cSubscription\x12\x18\n\x04path\x18\x01 \x01(\x0b\x32\n.gnmi.Path\x12$\n\x04mode\x18\x02 \x01(\x0e\x32\x16.gnmi.SubscriptionMode\x12\x17\n\x0fsample_interval\x18\x03 \x01(\x04\x12\x1a\n\x12suppress_redundant\x18\x04 \x01(\x08\x12\x1a\n\x12heartbeat_interval\x18\x05 \x01(\x04\x12\x0e\n\x06origin\x18\x06 \x01(\t\x12\x0e\n\x06target\x18\x07 \x01(\t\"\r\n\x0bPollRequest\"\'\n\tAliasList\x12\x1a\n\x05\x61lias\x18\x01 \x03(\x0b\x32\x0b.gnmi.Alias\"0\n\x05\x41lias\x12\x18\n\x04path\x18\x01 \x01(\x0b\x32\n.gnmi.Path\x12\r\n\x05\x61lias\x18\x02 \x01(\t\"\x11\n\x0fPublishResponse\"]\n\x0bLeaflistVal\x12!\n\x07\x65lement\x18\x01 \x03(\x0b\x32\x10.gnmi.TypedValue\x12+\n\x0cscalar_array\x18\x02 \x01(\x0b\x32\x11.gnmi.ScalarArrayB\x02\x18\x01*D\n\x08\x45ncoding\x12\x08\n\x04JSON\x10\x00\x12\t\n\x05\x42YTES\x10\x01\x12\t\n\x05PROTO\x10\x02\x12\t\n\x05\x41SCII\x10\x03\x12\r\n\tJSON_IETF\x10\x04*/\n\rSubscribeMode\x12\n\n\x06STREAM\x10\x00\x12\x08\n\x04ONCE\x10\x01\x12\x08\n\x04POLL\x10\x02*A\n\x10SubscriptionMode\x12\x12\n\x0eTARGET_DEFINED\x10\x00\x12\r\n\tON_CHANGE\x10\x01\x12\n\n\x06SAMPLE\x10\x02\x32\x89\x01\n\x04gNMI\x12\x42\n\tSubscribe\x12\x16.gnmi.SubscribeRequest\x1a\x17.gnmi.SubscribeResponse\"\x00(\x01\x30\x01\x12=\n\x07Publish\x12\x17.gnmi.SubscribeResponse\x1a\x15.gnmi.PublishResponse\"\x00(\x01\x42T\n com.github.openconfig.gnmi.protoB\x04GnmiZ*github.com/openconfig/gnmi/proto/gnmi;gnmib\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'tmp_proto.gnmi_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n com.github.openconfig.gnmi.protoB\004GnmiZ*github.com/openconfig/gnmi/proto/gnmi;gnmi'
  _globals['_PATH'].fields_by_name['element']._loaded_options = None
  _globals['_PATH'].fields_by_name['element']._serialized_options = b'\030\001'
  _globals['_PATHELEM_KEYENTRY']._loaded_options = None
  _globals['_PATHELEM_KEYENTRY']._serialized_options = b'8\001'
  _globals['_LEAFLISTVAL'].fields_by_name['scalar_array']._loaded_options = None
  _globals['_LEAFLISTVAL'].fields_by_name['scalar_array']._serialized_options = b'\030\001'
  _globals['_ENCODING']._serialized_start=2041
  _globals['_ENCODING']._serialized_end=2109
  _globals['_SUBSCRIBEMODE']._serialized_start=2111
  _globals['_SUBSCRIBEMODE']._serialized_end=2158
  _globals['_SUBSCRIPTIONMODE']._serialized_start=2160
  _globals['_SUBSCRIPTIONMODE']._serialized_end=2225
  _globals['_NOTIFICATION']._serialized_start=92
  _globals['_NOTIFICATION']._serialized_end=242
  _globals['_UPDATE']._serialized_start=244
  _globals['_UPDATE']._serialized_end=326
  _globals['_TYPEDVALUE']._serialized_start=329
  _globals['_TYPEDVALUE']._serialized_end=686
  _globals['_PATH']._serialized_start=688
  _globals['_PATH']._serialized_end=777
  _globals['_PATHELEM']._serialized_start=779
  _globals['_PATHELEM']._serialized_end=885
  _globals['_PATHELEM_KEYENTRY']._serialized_start=843
  _globals['_PATHELEM_KEYENTRY']._serialized_end=885
  _globals['_VALUE']._serialized_start=887
  _globals['_VALUE']._serialized_end=939
  _globals['_ERROR']._serialized_start=941
  _globals['_ERROR']._serialized_end=1015
  _globals['_DECIMAL64']._serialized_start=1017
  _globals['_DECIMAL64']._serialized_end=1063
  _globals['_SCALARARRAY']._serialized_start=1065
  _globals['_SCALARARRAY']._serialized_end=1113
  _globals['_SUBSCRIBERESPONSE']._serialized_start=1115
  _globals['_SUBSCRIBERESPONSE']._serialized_end=1239
  _globals['_SUBSCRIBEREQUEST']._serialized_start=1242
  _globals['_SUBSCRIBEREQUEST']._serialized_end=1387
  _globals['_SUBSCRIPTIONLIST']._serialized_start=1390
  _globals['_SUBSCRIPTIONLIST']._serialized_end=1625
  _globals['_SUBSCRIPTION']._serialized_start=1628
  _globals['_SUBSCRIPTION']._serialized_end=1819
  _globals['_POLLREQUEST']._serialized_start=1821
  _globals['_POLLREQUEST']._serialized_end=1834
  _globals['_ALIASLIST']._serialized_start=1836
  _globals['_ALIASLIST']._serialized_end=1875
  _globals['_ALIAS']._serialized_start=1877
  _globals['_ALIAS']._serialized_end=1925
  _globals['_PUBLISHRESPONSE']._serialized_start=1927
  _globals['_PUBLISHRESPONSE']._serialized_end=1944
  _globals['_LEAFLISTVAL']._serialized_start=1946
  _globals['_LEAFLISTVAL']._serialized_end=2039
  _globals['_GNMI']._serialized_start=2228
  _globals['_GNMI']._serialized_end=2365
# @@protoc_insertion_point(module_scope)
