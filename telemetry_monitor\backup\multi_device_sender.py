#!/usr/bin/env python3
"""
多设备数据发送器
同时模拟多个设备发送 telemetry 数据
"""

import socket
import json
import time
import threading
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor

class MultiDeviceDataSender:
    """多设备数据发送器"""
    
    def __init__(self, collector_ip="***************", collector_port=57400):
        self.collector_ip = collector_ip
        self.collector_port = collector_port
        self.running = False
        self.device_counters = {}
        self.device_threads = {}
        
        # 预定义的设备列表
        self.devices = [
            "nokia-alu-1",
            "nokia-alu-2", 
            "nokia-alu-3",
            "cisco-device-1"
        ]
    
    def send_device_data(self, device_name, interval=10):
        """为单个设备发送数据"""
        
        if device_name not in self.device_counters:
            self.device_counters[device_name] = 0
        
        print(f"🚀 启动设备 {device_name} 数据发送器 (间隔: {interval}秒)")
        
        while self.running:
            try:
                self.device_counters[device_name] += 1
                counter = self.device_counters[device_name]
                
                # 连接到收集器
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(5)
                sock.connect((self.collector_ip, self.collector_port))
                
                # 生成设备特定的数据
                message = self._generate_device_message(device_name, counter)
                
                # 发送数据
                json_data = json.dumps(message) + "\n"
                sock.send(json_data.encode('utf-8'))
                sock.close()
                
                # 显示发送状态
                self._display_send_status(device_name, counter, message)
                
                time.sleep(interval)
                
            except Exception as e:
                print(f"❌ 设备 {device_name} 发送数据失败: {e}")
                time.sleep(5)  # 失败后等待5秒重试
    
    def _generate_device_message(self, device_name, counter):
        """生成设备特定的消息"""
        
        # 基础消息结构
        message = {
            "timestamp": int(time.time() * 1000),
            "device": device_name,
            "sensor_group": "allSg",
            "subscription": "optical-monitoring",
            "sequence": counter,
            "data": {}
        }
        
        # 根据设备类型生成不同的数据
        if "nokia" in device_name.lower():
            message["data"] = self._generate_nokia_data(device_name, counter)
        elif "cisco" in device_name.lower():
            message["data"] = self._generate_cisco_data(device_name, counter)
        else:
            message["data"] = self._generate_generic_data(device_name, counter)
        
        return message
    
    def _generate_nokia_data(self, device_name, counter):
        """生成 Nokia 设备数据"""
        
        # 根据设备名称生成不同的端口和参数
        device_num = device_name.split('-')[-1] if '-' in device_name else "1"
        port_offset = int(device_num) if device_num.isdigit() else 1
        
        return {
            "openconfig-platform:components": {
                "component": [
                    {
                        "name": f"transceiver-{port_offset}/1/{(counter % 4) + 1}",
                        "openconfig-platform-transceiver:transceiver": {
                            "state": {
                                "present": "PRESENT",
                                "enabled": True,
                                "fault-condition": False,
                                "form-factor": "QSFP28",
                                "vendor": "Nokia",
                                "vendor-part": f"3HE1234{port_offset}AA",
                                "serial-no": f"ABC{123456789 + counter + port_offset * 1000}",
                                "connector-type": "LC"
                            },
                            "physical-channels": {
                                "channel": [
                                    {
                                        "index": 0,
                                        "state": {
                                            "index": 0,
                                            "description": f"Lane 0 - Device {device_num}",
                                            "tx-laser": True,
                                            "output-power": round(-2.0 + (counter % 10) * 0.1 + port_offset * 0.2, 2),
                                            "input-power": round(-3.0 + (counter % 8) * 0.1 + port_offset * 0.15, 2),
                                            "laser-bias-current": round(40.0 + (counter % 20) + port_offset * 2, 1),
                                            "target-output-power": -2.0
                                        }
                                    }
                                ]
                            }
                        }
                    },
                    {
                        "name": f"optical-channel-{port_offset}/1/1/1",
                        "openconfig-terminal-device:optical-channel": {
                            "state": {
                                "frequency": 196100000 + (counter % 100) * 1000 + port_offset * 50000,
                                "target-output-power": 0.0,
                                "operational-mode": port_offset,
                                "line-port": f"port-{port_offset}/1/1"
                            }
                        }
                    },
                    {
                        "name": f"cpu-{port_offset-1}",
                        "cpu": {
                            "openconfig-platform-cpu:utilization": {
                                "state": {
                                    "instant": round(20.0 + (counter % 30) + port_offset * 3, 1),
                                    "avg": round(25.0 + (counter % 15) + port_offset * 2, 1),
                                    "min": 15.0 + port_offset,
                                    "max": 45.0 + port_offset * 2,
                                    "interval": 300000000
                                }
                            }
                        }
                    }
                ]
            },
            "openconfig-terminal-device:terminal-device": {
                "logical-channels": {
                    "channel": [
                        {
                            "index": port_offset,
                            "otn": {
                                "state": {
                                    "tributary-slot-granularity": "TRIB_SLOT_1_25G",
                                    "pre-fec-ber": {
                                        "instant": round((1.0 + (counter % 5) * 0.1 + port_offset * 0.05) * 1e-12, 15),
                                        "avg": (1.1 + port_offset * 0.02) * 1e-12,
                                        "min": 9.8e-13,
                                        "max": 1.5e-12
                                    },
                                    "post-fec-ber": {
                                        "instant": 0.0,
                                        "avg": 0.0,
                                        "min": 0.0,
                                        "max": 0.0
                                    },
                                    "q-value": {
                                        "instant": round(15.0 + (counter % 10) * 0.1 + port_offset * 0.2, 1),
                                        "avg": 15.1 + port_offset * 0.1,
                                        "min": 14.8,
                                        "max": 15.5
                                    }
                                }
                            },
                            "ethernet": {
                                "state": {
                                    "in-mac-control-frames": (counter % 100) + port_offset * 10,
                                    "in-mac-pause-frames": 0,
                                    "in-oversize-frames": (counter % 10) + port_offset,
                                    "in-undersize-frames": 0,
                                    "in-fragment-frames": 0,
                                    "in-8021q-frames": 1000000 + counter * 1000 + port_offset * 100000,
                                    "out-mac-control-frames": (counter % 95) + port_offset * 8,
                                    "out-mac-pause-frames": 0,
                                    "out-8021q-frames": 1000000 + counter * 950 + port_offset * 95000
                                }
                            }
                        }
                    ]
                }
            }
        }
    
    def _generate_cisco_data(self, device_name, counter):
        """生成 Cisco 设备数据（简化版）"""
        
        device_num = device_name.split('-')[-1] if '-' in device_name else "1"
        port_offset = int(device_num) if device_num.isdigit() else 1
        
        return {
            "cisco-platform:components": {
                "component": [
                    {
                        "name": f"GigabitEthernet{port_offset}/0/{(counter % 4) + 1}",
                        "state": {
                            "type": "PORT",
                            "description": f"Cisco Interface {port_offset}",
                            "oper-status": "UP",
                            "admin-status": "UP"
                        },
                        "counters": {
                            "in-octets": 2000000000 + counter * 1500 + port_offset * 200000,
                            "out-octets": 1800000000 + counter * 1200 + port_offset * 180000,
                            "in-packets": 15000000 + counter * 100 + port_offset * 10000,
                            "out-packets": 14000000 + counter * 95 + port_offset * 9500
                        }
                    }
                ]
            },
            "cisco-system:system": {
                "cpu": {
                    "utilization": {
                        "instant": round(15.0 + (counter % 25) + port_offset * 2, 1),
                        "avg": round(18.0 + (counter % 12) + port_offset * 1.5, 1)
                    }
                },
                "memory": {
                    "utilization": {
                        "instant": round(45.0 + (counter % 20) + port_offset * 3, 1),
                        "avg": round(48.0 + (counter % 10) + port_offset * 2, 1)
                    }
                }
            }
        }
    
    def _generate_generic_data(self, device_name, counter):
        """生成通用设备数据"""
        
        return {
            "generic-device": {
                "name": device_name,
                "status": "active",
                "metrics": {
                    "cpu_usage": round(20.0 + (counter % 30), 1),
                    "memory_usage": round(50.0 + (counter % 25), 1),
                    "uptime": 86400 + counter * 60,
                    "temperature": round(35.0 + (counter % 15), 1)
                }
            }
        }
    
    def _display_send_status(self, device_name, counter, message):
        """显示发送状态"""
        
        timestamp = datetime.now().strftime('%H:%M:%S')
        
        # 提取关键信息用于显示
        cpu_util = "N/A"
        transceiver_power = "N/A"
        
        try:
            if "nokia" in device_name.lower():
                # Nokia 设备信息
                components = message['data']['openconfig-platform:components']['component']
                for component in components:
                    if 'cpu' in component.get('name', ''):
                        cpu_data = component.get('cpu', {}).get('openconfig-platform-cpu:utilization', {}).get('state', {})
                        cpu_util = f"{cpu_data.get('instant', 'N/A')}%"
                    elif 'transceiver' in component.get('name', ''):
                        transceiver_data = component.get('openconfig-platform-transceiver:transceiver', {})
                        channels = transceiver_data.get('physical-channels', {}).get('channel', [])
                        if channels:
                            transceiver_power = f"{channels[0]['state'].get('output-power', 'N/A')} dBm"
            
            elif "cisco" in device_name.lower():
                # Cisco 设备信息
                cpu_data = message['data']['cisco-system:system']['cpu']['utilization']
                cpu_util = f"{cpu_data.get('instant', 'N/A')}%"
        
        except:
            pass
        
        print(f"📊 [{timestamp}] {device_name} | 消息 #{counter} | CPU: {cpu_util} | 功率: {transceiver_power}")
    
    def start_all_devices(self, intervals=None):
        """启动所有设备的数据发送"""
        
        if intervals is None:
            intervals = {device: 10 for device in self.devices}  # 默认10秒间隔
        
        print(f"🚀 启动多设备数据发送器")
        print(f"📡 目标收集器: {self.collector_ip}:{self.collector_port}")
        print(f"📱 设备数量: {len(self.devices)}")
        print(f"⏰ 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)
        
        self.running = True
        
        # 为每个设备启动发送线程
        with ThreadPoolExecutor(max_workers=len(self.devices)) as executor:
            futures = []
            
            for device in self.devices:
                interval = intervals.get(device, 10)
                future = executor.submit(self.send_device_data, device, interval)
                futures.append(future)
                time.sleep(1)  # 错开启动时间
            
            try:
                # 等待所有线程完成（实际上会一直运行）
                for future in futures:
                    future.result()
            except KeyboardInterrupt:
                print("\n🛑 收到停止信号...")
                self.running = False
    
    def start_selected_devices(self, device_names, interval=10):
        """启动指定设备的数据发送"""
        
        print(f"🚀 启动指定设备数据发送器")
        print(f"📡 目标收集器: {self.collector_ip}:{self.collector_port}")
        print(f"📱 选定设备: {', '.join(device_names)}")
        print(f"⏰ 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)
        
        self.running = True
        
        # 为选定设备启动发送线程
        with ThreadPoolExecutor(max_workers=len(device_names)) as executor:
            futures = []
            
            for device in device_names:
                future = executor.submit(self.send_device_data, device, interval)
                futures.append(future)
                time.sleep(1)  # 错开启动时间
            
            try:
                for future in futures:
                    future.result()
            except KeyboardInterrupt:
                print("\n🛑 收到停止信号...")
                self.running = False
    
    def stop(self):
        """停止所有设备的数据发送"""
        self.running = False
        print("🛑 多设备数据发送器已停止")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='多设备数据发送器')
    parser.add_argument('--devices', nargs='+', help='指定设备名称')
    parser.add_argument('--interval', type=int, default=10, help='发送间隔（秒，默认10）')
    parser.add_argument('--collector-ip', default='***************', help='收集器IP地址')
    parser.add_argument('--collector-port', type=int, default=57400, help='收集器端口')
    
    args = parser.parse_args()
    
    sender = MultiDeviceDataSender(args.collector_ip, args.collector_port)
    
    try:
        if args.devices:
            sender.start_selected_devices(args.devices, args.interval)
        else:
            sender.start_all_devices()
    except KeyboardInterrupt:
        print("\n🛑 停止多设备数据发送")
        sender.stop()
    except Exception as e:
        print(f"❌ 程序运行失败: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    import sys
    sys.exit(main())
