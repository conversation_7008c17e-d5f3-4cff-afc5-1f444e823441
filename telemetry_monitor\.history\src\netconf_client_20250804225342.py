#!/usr/bin/env python3
"""
NETCONF 连接管理模块
提供与网络设备的 NETCONF 连接管理功能
"""

import logging
import time
from typing import Dict, Any, Optional, List
from contextlib import contextmanager

import yaml
from ncclient import manager
from ncclient.operations import RPCError
from ncclient.transport.errors import TransportError, AuthenticationError
from pydantic import BaseModel, Field, validator


class DeviceConfig(BaseModel):
    """设备配置模型"""
    name: str = Field(..., description="设备名称")
    host: str = Field(..., description="设备 IP 地址")
    port: int = Field(default=830, description="NETCONF 端口")
    username: str = Field(..., description="用户名")
    password: Optional[str] = Field(None, description="密码")
    ssh_key_file: Optional[str] = Field(None, description="SSH 私钥文件路径")
    timeout: int = Field(default=30, description="连接超时时间")
    device_type: str = Field(default="generic", description="设备类型")
    # Nokia ALU 特定参数
    hostkey_verify: bool = Field(default=True, description="是否验证主机密钥")
    look_for_keys: bool = Field(default=True, description="是否查找 SSH 密钥")
    allow_agent: bool = Field(default=True, description="是否允许 SSH 代理")
    
    @validator('port')
    def validate_port(cls, v):
        if not 1 <= v <= 65535:
            raise ValueError('端口号必须在 1-65535 范围内')
        return v


class ConnectionSettings(BaseModel):
    """连接设置模型"""
    default_timeout: int = Field(default=30, description="默认超时时间")
    retry_count: int = Field(default=3, description="重试次数")
    retry_interval: int = Field(default=5, description="重试间隔")
    verify_ssl: bool = Field(default=False, description="是否验证 SSL 证书")
    session_keepalive: int = Field(default=60, description="会话保持时间")


class NetconfClient:
    """NETCONF 客户端管理器"""
    
    def __init__(self, config_file: Optional[str] = None):
        """
        初始化 NETCONF 客户端
        
        Args:
            config_file: 配置文件路径
        """
        self.logger = logging.getLogger(__name__)
        self.devices: Dict[str, DeviceConfig] = {}
        self.connections: Dict[str, manager.Manager] = {}
        self.settings = ConnectionSettings()
        
        if config_file:
            self.load_config(config_file)
    
    def load_config(self, config_file: str) -> None:
        """
        加载设备配置文件
        
        Args:
            config_file: 配置文件路径
        """
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            # 加载设备配置
            if 'devices' in config:
                for device_data in config['devices']:
                    device = DeviceConfig(**device_data)
                    self.devices[device.name] = device
                    self.logger.info(f"已加载设备配置: {device.name} ({device.host})")
            
            # 加载连接设置
            if 'connection_settings' in config:
                self.settings = ConnectionSettings(**config['connection_settings'])
                self.logger.info("已加载连接设置")
                
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            raise
    
    def add_device(self, device_config: Dict[str, Any]) -> None:
        """
        添加设备配置
        
        Args:
            device_config: 设备配置字典
        """
        device = DeviceConfig(**device_config)
        self.devices[device.name] = device
        self.logger.info(f"已添加设备: {device.name} ({device.host})")
    
    def connect(self, device_name: str) -> manager.Manager:
        """
        连接到指定设备
        
        Args:
            device_name: 设备名称
            
        Returns:
            NETCONF 管理器实例
            
        Raises:
            ValueError: 设备不存在
            ConnectionError: 连接失败
        """
        if device_name not in self.devices:
            raise ValueError(f"设备 '{device_name}' 不存在")
        
        device = self.devices[device_name]
        
        # 如果已经连接，返回现有连接
        if device_name in self.connections:
            conn = self.connections[device_name]
            if conn.connected:
                return conn
            else:
                # 连接已断开，清理并重新连接
                del self.connections[device_name]
        
        # 建立新连接
        return self._establish_connection(device_name, device)
    
    def _establish_connection(self, device_name: str, device: DeviceConfig) -> manager.Manager:
        """
        建立 NETCONF 连接
        
        Args:
            device_name: 设备名称
            device: 设备配置
            
        Returns:
            NETCONF 管理器实例
        """
        connection_params = {
            'host': device.host,
            'port': device.port,
            'username': device.username,
            'timeout': device.timeout,
            'hostkey_verify': self.settings.verify_ssl,
            'device_params': {'name': device.device_type}
        }
        
        # 设置认证方式
        if device.ssh_key_file:
            connection_params['key_filename'] = device.ssh_key_file
        elif device.password:
            connection_params['password'] = device.password
        else:
            raise ValueError(f"设备 '{device_name}' 缺少认证信息（密码或 SSH 密钥）")
        
        # 重试连接
        last_error = None
        for attempt in range(self.settings.retry_count):
            try:
                self.logger.info(f"正在连接设备 '{device_name}' (尝试 {attempt + 1}/{self.settings.retry_count})")
                
                conn = manager.connect(**connection_params)
                self.connections[device_name] = conn
                
                self.logger.info(f"成功连接到设备 '{device_name}' ({device.host}:{device.port})")
                return conn
                
            except (TransportError, AuthenticationError, Exception) as e:
                last_error = e
                self.logger.warning(f"连接设备 '{device_name}' 失败 (尝试 {attempt + 1}): {e}")
                
                if attempt < self.settings.retry_count - 1:
                    time.sleep(self.settings.retry_interval)
        
        # 所有重试都失败
        error_msg = f"连接设备 '{device_name}' 失败，已重试 {self.settings.retry_count} 次"
        self.logger.error(f"{error_msg}: {last_error}")
        raise ConnectionError(f"{error_msg}: {last_error}")
    
    def disconnect(self, device_name: str) -> None:
        """
        断开设备连接
        
        Args:
            device_name: 设备名称
        """
        if device_name in self.connections:
            try:
                self.connections[device_name].close_session()
                self.logger.info(f"已断开设备 '{device_name}' 的连接")
            except Exception as e:
                self.logger.warning(f"断开设备 '{device_name}' 连接时出错: {e}")
            finally:
                del self.connections[device_name]
    
    def disconnect_all(self) -> None:
        """断开所有设备连接"""
        device_names = list(self.connections.keys())
        for device_name in device_names:
            self.disconnect(device_name)
    
    @contextmanager
    def get_connection(self, device_name: str):
        """
        获取设备连接的上下文管理器
        
        Args:
            device_name: 设备名称
            
        Yields:
            NETCONF 管理器实例
        """
        conn = None
        try:
            conn = self.connect(device_name)
            yield conn
        finally:
            # 可选择是否自动断开连接
            # self.disconnect(device_name)
            pass
    
    def execute_rpc(self, device_name: str, rpc_request: str) -> str:
        """
        执行 RPC 请求
        
        Args:
            device_name: 设备名称
            rpc_request: RPC 请求 XML
            
        Returns:
            RPC 响应 XML
            
        Raises:
            RPCError: RPC 执行错误
        """
        with self.get_connection(device_name) as conn:
            try:
                self.logger.debug(f"执行 RPC 请求到设备 '{device_name}': {rpc_request}")
                response = conn.dispatch(rpc_request)
                self.logger.debug(f"收到 RPC 响应: {response}")
                return str(response)
            except RPCError as e:
                self.logger.error(f"RPC 执行失败: {e}")
                raise
    
    def get_config(self, device_name: str, source: str = 'running') -> str:
        """
        获取设备配置
        
        Args:
            device_name: 设备名称
            source: 配置源 ('running', 'candidate', 'startup')
            
        Returns:
            配置 XML
        """
        with self.get_connection(device_name) as conn:
            try:
                config = conn.get_config(source=source)
                return str(config)
            except RPCError as e:
                self.logger.error(f"获取配置失败: {e}")
                raise
    
    def edit_config(self, device_name: str, config_xml: str, target: str = 'candidate') -> str:
        """
        编辑设备配置
        
        Args:
            device_name: 设备名称
            config_xml: 配置 XML
            target: 目标配置 ('candidate', 'running')
            
        Returns:
            操作结果 XML
        """
        with self.get_connection(device_name) as conn:
            try:
                self.logger.info(f"正在编辑设备 '{device_name}' 的配置")
                result = conn.edit_config(target=target, config=config_xml)
                self.logger.info(f"配置编辑成功")
                return str(result)
            except RPCError as e:
                self.logger.error(f"配置编辑失败: {e}")
                raise
    
    def commit(self, device_name: str) -> str:
        """
        提交配置更改
        
        Args:
            device_name: 设备名称
            
        Returns:
            提交结果 XML
        """
        with self.get_connection(device_name) as conn:
            try:
                self.logger.info(f"正在提交设备 '{device_name}' 的配置更改")
                result = conn.commit()
                self.logger.info(f"配置提交成功")
                return str(result)
            except RPCError as e:
                self.logger.error(f"配置提交失败: {e}")
                raise
    
    def validate_connection(self, device_name: str) -> bool:
        """
        验证设备连接
        
        Args:
            device_name: 设备名称
            
        Returns:
            连接是否有效
        """
        try:
            with self.get_connection(device_name) as conn:
                # 尝试获取设备能力
                capabilities = conn.server_capabilities
                self.logger.info(f"设备 '{device_name}' 连接验证成功，支持 {len(capabilities)} 个能力")
                return True
        except Exception as e:
            self.logger.error(f"设备 '{device_name}' 连接验证失败: {e}")
            return False
    
    def get_device_capabilities(self, device_name: str) -> List[str]:
        """
        获取设备支持的能力
        
        Args:
            device_name: 设备名称
            
        Returns:
            能力列表
        """
        with self.get_connection(device_name) as conn:
            return list(conn.server_capabilities)
    
    def list_devices(self) -> List[str]:
        """
        获取所有配置的设备名称
        
        Returns:
            设备名称列表
        """
        return list(self.devices.keys())
    
    def get_device_info(self, device_name: str) -> Dict[str, Any]:
        """
        获取设备信息
        
        Args:
            device_name: 设备名称
            
        Returns:
            设备信息字典
        """
        if device_name not in self.devices:
            raise ValueError(f"设备 '{device_name}' 不存在")
        
        device = self.devices[device_name]
        is_connected = device_name in self.connections and self.connections[device_name].connected
        
        return {
            'name': device.name,
            'host': device.host,
            'port': device.port,
            'username': device.username,
            'device_type': device.device_type,
            'timeout': device.timeout,
            'connected': is_connected
        }