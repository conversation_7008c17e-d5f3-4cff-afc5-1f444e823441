# Nokia ALU 设备连接配置
devices:
  # Nokia ALU 真实设备
  - name: "nokia-alu-1"
    host: "***************"
    port: 830
    username: "admin"
    password: "Nokia#123"
    timeout: 30
    device_type: "alu"
    # Nokia ALU 特定连接参数
    hostkey_verify: false
    look_for_keys: false
    allow_agent: false
    
  # 多设备配置示例
  - name: "nokia-alu-2"
    host: "***************"
    port: 830
    username: "admin"
    password: "Nokia#123"
    timeout: 30
    device_type: "alu"
    hostkey_verify: false
    look_for_keys: false
    allow_agent: false

  - name: "nokia-alu-3"
    host: "***************"
    port: 830
    username: "admin"
    password: "Nokia#123"
    timeout: 30
    device_type: "alu"
    hostkey_verify: false
    look_for_keys: false
    allow_agent: false

  - name: "cisco-device-1"
    host: "***************"
    port: 830
    username: "admin"
    password: "Cisco#123"
    timeout: 30
    device_type: "cisco"
    hostkey_verify: false
    look_for_keys: false
    allow_agent: false

  - name: "router-example"
    host: "***************"
    port: 830
    username: "admin"
    password: "Nokia#123"
    timeout: 30
    device_type: "alu"
    hostkey_verify: false
    look_for_keys: false
    allow_agent: false

# 全局连接设置
connection_settings:
  # 默认超时时间（秒）
  default_timeout: 30
  # 重试次数
  retry_count: 3
  # 重试间隔（秒）
  retry_interval: 5
  # 是否验证 SSL 证书
  verify_ssl: false
  # NETCONF 会话保持时间（秒）
  session_keepalive: 60