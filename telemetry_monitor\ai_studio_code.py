import grpc
import concurrent.futures
import json
import os
import threading
import time
from datetime import datetime
from google.protobuf import json_format
import binascii
import re

# 导入生成的gNMI protobuf文件
from tmp_proto import gnmi_base_pb2
from tmp_proto import gnmi_base_pb2_grpc
from tmp_proto import gnmi_dialout_pb2
from tmp_proto import gnmi_dialout_pb2_grpc

# 存储每个设备的遥测数据文件句柄和锁
device_data_files = {}
device_data_locks = {}

# 数据存储目录
DATA_DIR = "telemetry_data"
os.makedirs(DATA_DIR, exist_ok=True)

def safe_decode_bytes(data):
    """
    安全解码字节数据，处理乱码问题。返回一个字典，包含多种解码尝试。
    """
    if isinstance(data, bytes):
        decoded_attempts = {
            "original_hex": binascii.hexlify(data).decode('ascii'),
            "length": len(data)
        }
        try:
            # 尝试UTF-8解码
            decoded_utf8 = data.decode('utf-8')
            decoded_attempts["utf8"] = decoded_utf8
        except UnicodeDecodeError:
            decoded_attempts["utf8_error"] = True
        
        try:
            # 尝试latin-1解码
            decoded_latin1 = data.decode('latin-1')
            decoded_attempts["latin1"] = decoded_latin1
        except UnicodeDecodeError:
            decoded_attempts["latin1_error"] = True

        decoded_replace = data.decode('utf-8', errors='replace')
        decoded_attempts["utf8_replace"] = decoded_replace
        
        return decoded_attempts
    return {"original": str(data)}

def clean_json_string(json_str):
    """
    清理JSON字符串，尝试移除可能的前缀字符、控制字符和格式问题，
    并提取最可能的JSON对象或数组。
    """
    if not isinstance(json_str, str):
        return str(json_str)
    
    original_str = json_str
    print(f"原始字符串 (clean_json_string): '{original_str}' (长度: {len(original_str)})")
    
    # 移除ASCII控制字符（0x00-0x1F）和DEL（0x7F）以及一些非标准但可能出现的控制字符（0x80-0x9F）
    # 确保不移除合法的JSON字符，例如\n, \t, \r
    cleaned_str = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\x9f]', '', json_str) # Removed \n \t \r
    print(f"移除控制字符后 (clean_json_string): '{cleaned_str}'")
    
    # 尝试使用正则表达式找到最外层的JSON对象或数组
    # Pattern to find a JSON object or array, non-greedy, potentially with leading/trailing non-JSON characters
    json_match = re.search(r'(\{.*?\}|\[.*?\])', cleaned_str, re.DOTALL)
    
    if json_match:
        extracted_json = json_match.group(0)
        print(f"正则提取的JSON部分: '{extracted_json}'")
        return extracted_json
    
    # 如果正则提取失败，尝试回退到旧的查找 '{'/'[' 和 '}'/']' 方式
    print(f"正则提取失败，尝试回退到基于索引的提取。")
    
    # 移除前后空白字符
    json_str = original_str.strip() # Use original_str for this path
    
    # 查找JSON开始位置（查找第一个 '{' 或 '['）
    json_start = -1
    for i, char in enumerate(json_str):
        if char in ['{', '[']:
            json_start = i
            break
    
    if json_start == -1:
        print(f"未找到JSON开始标识符")
        return original_str # No JSON structure found
    
    # 查找JSON结束位置（查找最后一个 '}' 或 ']'）
    # More robust, basic brace/bracket balancing check
    balance = 0
    in_quote = False
    escaped = False
    json_end = -1
    
    for i in range(json_start, len(json_str)):
        char = json_str[i]
        if escaped:
            escaped = False
            continue
        if char == '\\':
            escaped = True
            continue

        if char == '"':
            in_quote = not in_quote
        
        if not in_quote:
            if char == '{' or char == '[':
                balance += 1
            elif char == '}' or char == ']':
                balance -= 1
        
        if balance == 0 and (char == '}' or char == ']'):
            json_end = i + 1
            break
    
    if json_end > json_start:
        json_str = json_str[json_start:json_end]
        print(f"平衡提取后: '{json_str}'")
    elif json_start != -1: # If we found a start but no balanced end
        json_str = json_str[json_start:]
        print(f"找到开始但未找到平衡结束，从开始到末尾提取: '{json_str}'")
    else:
        json_str = original_str # Fallback

    print(f"清理后的JSON字符串 (最终): '{json_str}'")
    return json_str

def try_parse_json(data_str):
    """
    尝试多种方式解析JSON字符串，并返回解析结果。
    """
    print(f"🔍 尝试解析JSON数据...")
    print(f"输入数据类型 (try_parse_json): {type(data_str)}")
    print(f"输入数据 (try_parse_json): '{data_str}'")
    
    if not isinstance(data_str, str):
        data_str = str(data_str)
    
    # 方法1：直接解析
    try:
        result = json.loads(data_str)
        print(f"✅ 直接JSON解析成功: {type(result)}")
        return result
    except json.JSONDecodeError as e:
        print(f"❌ 直接JSON解析失败: {e}")
        pass # Continue to next method
    
    # 方法2：清理后解析
    print(f"尝试清理并再次解析...")
    cleaned_str = clean_json_string(data_str)
    if cleaned_str != data_str: # Only try parsing if cleaning actually changed something
        try:
            result = json.loads(cleaned_str)
            print(f"✅ 清理后JSON解析成功: {type(result)}")
            return result
        except json.JSONDecodeError as e:
            print(f"❌ 清理后JSON解析失败: {e}")
            pass
    else:
        print(f"清理未改变字符串，跳过再次解析。")
    
    # If all JSON parsing attempts fail, return the original or cleaned string
    print(f"所有JSON解析尝试失败，返回原始或清理后的字符串。")
    return data_str # Or cleaned_str, depending on desired fallback. Using data_str as it might be non-JSON but still useful.


def bytes_to_readable_string(data):
    """
    将字节数据转换为可读字符串，包含十六进制表示，并返回字典。
    """
    if isinstance(data, bytes):
        decoded_info = safe_decode_bytes(data)
        return decoded_info
    return {"string_repr": str(data)}


# Renamed original get_json_value to handle_typed_value to clarify its role
# This function is now specifically for parsing TypedValue objects
def handle_typed_value(typed_value):
    """
    从TypedValue中提取值，增强错误处理和调试信息。
    """
    print(f"=== TypedValue 详细分析 ===")
    print(f"TypedValue 对象类型: {type(typed_value)}")
    
    available_fields = []
    value_field_names = ['json_val', 'json_ietf_val', 'string_val', 'int_val', 
                         'uint_val', 'bool_val', 'bytes_val', 'float_val', 
                         'double_val', 'decimal_val', 'leaflist_val', 'any_val', 
                         'proto_bytes', 'ascii_val'] # Added ascii_val as per your proto

    for field_name in value_field_names:
        try:
            if typed_value.HasField(field_name):
                field_value = getattr(typed_value, field_name)
                print(f"  ✓ {field_name}: (HasField) {type(field_value)}")
                available_fields.append(field_name)
                break 
            elif field_name == 'leaflist_val':
                field_value = getattr(typed_value, field_name)
                if field_value and len(field_value.element) > 0:
                    print(f"  ✓ {field_name}: (has elements) {type(field_value)}")
                    available_fields.append(field_name)
                    break
            elif field_name in ['string_val', 'ascii_val'] and getattr(typed_value, field_name):
                field_value = getattr(typed_value, field_name)
                print(f"  ✓ {field_name}: (not empty) {type(field_value)}")
                available_fields.append(field_name)
                break
            elif field_name in ['bytes_val', 'json_val', 'json_ietf_val', 'proto_bytes'] and getattr(typed_value, field_name) != b'':
                field_value = getattr(typed_value, field_name)
                print(f"  ✓ {field_name}: (not empty) {type(field_value)}")
                available_fields.append(field_name)
                break
            else:
                print(f"  ✗ {field_name}: (未设置或为空)")
        except ValueError:
            print(f"  ✗ {field_name}: (非oneof成员或无法检查)")
            pass
        except AttributeError:
            print(f"  ✗ {field_name}: (字段不存在)")
            pass

    print(f"活跃字段 (handle_typed_value): {available_fields}")
    
    if not available_fields:
        print(f"❌ 未识别的 TypedValue 类型，没有活跃字段。尝试完整字典转换。")
        try:
            typed_value_dict = json_format.MessageToDict(typed_value, preserving_proto_field_name=True)
            print(f"TypedValue 完整字典表示: {json.dumps(typed_value_dict, indent=2, ensure_ascii=False)}")
            
            if len(typed_value_dict) == 1 and list(typed_value_dict.keys())[0].endswith('_val'):
                 return typed_value_dict[list(typed_value_dict.keys())[0]]
            return typed_value_dict
        except Exception as e:
            print(f"TypedValue 字典转换失败: {e}")
        return {
            "error": "无法识别的TypedValue类型",
            "string_representation": str(typed_value)
        }

    active_field_name = available_fields[0]
    raw_value = getattr(typed_value, active_field_name)

    print(f"处理活跃字段: {active_field_name}, 原始值类型: {type(raw_value)}")
    
    if active_field_name in ["json_val", "json_ietf_val", "bytes_val", "proto_bytes", "ascii_val"]:
        print(f"尝试从字节/字符串字段 '{active_field_name}' 提取JSON/字符串...")
        
        string_to_parse = None
        if isinstance(raw_value, bytes):
            decoded_info = bytes_to_readable_string(raw_value)
            print(f"  解码尝试结果: {decoded_info}")
            string_to_parse = decoded_info.get("utf8") or decoded_info.get("latin1") or decoded_info.get("utf8_replace")
        elif isinstance(raw_value, str):
            string_to_parse = raw_value

        if string_to_parse:
            parsed_json = try_parse_json(string_to_parse)
            if isinstance(parsed_json, (dict, list)):
                print(f"  ✅ 成功解析为JSON: {type(parsed_json)}")
                return parsed_json
            else:
                print(f"  ❌ 未能解析为JSON，返回处理后的字符串: '{parsed_json}'")
                return parsed_json
        else:
            print(f"  ⚠️ 未能解码为字符串，返回原始字节信息。")
            return decoded_info if isinstance(raw_value, bytes) else str(raw_value)
            
    elif active_field_name in ["string_val"]: # string_val is always a string, no need for try_parse_json if it's not expected to be JSON.
        print(f"处理 string_val: '{raw_value}'")
        return raw_value # Assuming string_val is just a string, not embedded JSON. If it can be JSON, pass through try_parse_json.
        
    elif active_field_name in ["int_val", "uint_val", "bool_val", "float_val", "double_val"]:
        print(f"处理数值/布尔类型: {raw_value}")
        return raw_value
        
    elif active_field_name == "decimal_val":
        decimal_data = {
            "digits": raw_value.digits,
            "precision": raw_value.precision
        }
        print(f"处理 decimal_val: {decimal_data}")
        return decimal_data
        
    elif active_field_name == "leaflist_val":
        print(f"处理 leaflist_val，包含 {len(raw_value.element)} 个元素")
        leaflist_data = []
        for i, elem in enumerate(raw_value.element):
            print(f"  处理 leaflist 元素 {i}:")
            elem_value = handle_typed_value(elem) # Recursive call
            leaflist_data.append(elem_value)
        print(f"leaflist_val 完整结果: {leaflist_data}")
        return leaflist_data
        
    elif active_field_name == "any_val":
        print(f"处理 any_val 字段...")
        any_val = raw_value
        print(f"any_val type_url: {any_val.type_url}")
        raw_data_bytes = any_val.value
        decoded_info = bytes_to_readable_string(raw_data_bytes)
        print(f"any_val 原始数据字节: {decoded_info}")
        
        string_to_parse = decoded_info.get("utf8") or decoded_info.get("latin1") or decoded_info.get("utf8_replace")
        
        parsed_value = string_to_parse
        try:
            if string_to_parse and ("json" in any_val.type_url or "struct" in any_val.type_url.lower()):
                parsed_value = try_parse_json(string_to_parse)
            elif string_to_parse:
                parsed_value = try_parse_json(string_to_parse)
            
            if isinstance(parsed_value, (dict, list)):
                print(f"  ✅ any_val 成功解析为JSON: {type(parsed_value)}")
                return {
                    "type_url": any_val.type_url,
                    "value": parsed_value
                }
            else:
                print(f"  ❌ any_val 未能解析为JSON，返回解码字符串: '{parsed_value}'")
                return {
                    "type_url": any_val.type_url,
                    "value": parsed_value
                }
        except Exception as e:
            print(f"any_val 处理异常: {e}")
            return {
                "type_url": any_val.type_url,
                "value": decoded_info
            }
    else:
        print(f"处理未知或复杂字段 '{active_field_name}'。尝试转换为字典。")
        try:
            return json_format.MessageToDict(raw_value, preserving_proto_field_name=True)
        except Exception as e:
            print(f"将字段 '{active_field_name}' 转换为字典失败: {e}")
            return {
                "error": f"无法处理字段 {active_field_name}",
                "value": str(raw_value)
            }


def path_to_string(path):
    """
    将gNMI Path对象转换为字符串表示。
    """
    path_elements = []
    if path.origin:
        path_elements.append(path.origin + ":")
        
    for elem in path.elem:
        name = elem.name
        if elem.key:
            key_parts = []
            for k, v in elem.key.items():
                key_parts.append(f"{k}={v}")
            name += "[" + ",".join(key_parts) + "]"
        path_elements.append(name)
    
    full_path = "/" + "/".join(path_elements)
    return full_path

def print_raw_message(message, message_type="Unknown"):
    """
    打印原始消息的完整信息
    """
    print(f"\n{'='*80}")
    print(f"原始 {message_type} 消息:")
    print(f"{'='*80}")
    
    # 1. 打印消息的字符串表示
    print(f"[字符串表示]")
    print(str(message))
    print(f"{'-'*40}")
    
    # 2. 尝试转换为字典
    try:
        message_dict = json_format.MessageToDict(message, preserving_proto_field_name=True)
        print(f"[JSON字典表示]")
        print(json.dumps(message_dict, indent=2, ensure_ascii=False))
    except Exception as e:
        print(f"[JSON转换失败] {e}")
    
    print(f"{'='*80}\n")

class TelemetryServicer(gnmi_dialout_pb2_grpc.gNMIDialoutServicer):
    """
    gNMI Dialout Telemetry Servicer实现。
    """
    def Publish(self, request_iterator, context):
        peer_address = context.peer()
        # 从 peer_address 中提取 IP 地址
        if peer_address.startswith("ipv4:"):
            device_ip = peer_address.split(":")[1]
        elif peer_address.startswith("ipv6:"):
            if '[' in peer_address and ']' in peer_address:
                device_ip = peer_address.split('[')[1].split(']')[0]
            else:
                device_ip = peer_address.split(":")[1]
        else:
            device_ip = peer_address.split(":")[0]

        print(f"\n🔗 收到来自设备 {device_ip} 的连接 (peer: {peer_address})")

        current_time_str = datetime.now().strftime("%Y%m%d_%H%M%S") 
        file_name = f"{device_ip}_{current_time_str}.json"
        
        file_path = os.path.join(DATA_DIR, file_name)

        if device_ip not in device_data_files or device_data_files[device_ip].name != file_path:
            if device_ip in device_data_files:
                device_data_files[device_ip].close()
                print(f"📁 关闭设备 {device_ip} 的旧文件")
            device_data_files[device_ip] = open(file_path, 'a', encoding='utf-8')
            device_data_locks[device_ip] = threading.Lock()
            print(f"📁 为设备 {device_ip} 创建数据文件: {file_path}")

        message_count = 0
        try:
            for request in request_iterator:
                message_count += 1
                print(f"\n📥 设备 {device_ip} - 消息 #{message_count}")
                
                print_raw_message(request, f"gNMI Dialout Request #{message_count}")

                oneof_field = request.WhichOneof('response')
                print(f"🏷️  消息类型: {oneof_field}")
                
                if oneof_field == "update":
                    self._handle_update(request.update, device_ip, message_count)
                elif oneof_field == "sync_response":
                    self._handle_sync_response(request.sync_response, device_ip, message_count)
                elif oneof_field == "error": 
                    self._handle_error(request.error, device_ip, message_count)
                else:
                    print(f"⚠️  设备 {device_ip} - 消息 #{message_count}: 未知或空消息类型")
                    unknown_data = {
                        "message_number": message_count,
                        "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f'),
                        "device_ip": device_ip,
                        "message_type": "unknown",
                        "oneof_field": oneof_field,
                        "raw_message": self._safe_message_to_dict(request)
                    }
                    self._save_to_file(device_ip, unknown_data)
                    
        except grpc.RpcError as e:
            print(f"❌ 设备 {device_ip} gRPC流错误: {e.code()} - {e.details()}")
            if e.code() == grpc.StatusCode.UNAVAILABLE:
                print(f"  (设备可能已断开连接或服务器正在关闭)")
            elif e.code() == grpc.StatusCode.CANCELLED:
                print(f"  (流被取消，通常发生在客户端关闭连接时)")
        except Exception as e:
            print(f"❌ 设备 {device_ip} 未知错误: {e}")
            import traceback
            traceback.print_exc()
        finally:
            print(f"📤 设备 {device_ip} 连接结束，共处理 {message_count} 条消息")
            return gnmi_base_pb2.PublishResponse()

    def _handle_update(self, notification, device_ip, message_count):
        """处理更新消息"""
        print(f"📊 处理更新消息...")
        
        print_raw_message(notification, f"Notification #{message_count}")
        
        raw_timestamp = notification.timestamp
        readable_timestamp = datetime.fromtimestamp(raw_timestamp / 1_000_000_000).strftime('%Y-%m-%d %H:%M:%S.%f') if raw_timestamp else "N/A"
        
        prefix_path = ""
        if notification.HasField("prefix"):
            print(f"🏠 处理前缀路径...")
            prefix_path = path_to_string(notification.prefix)
        
        data_to_log = {
            "message_number": message_count,
            "timestamp": readable_timestamp,
            "raw_timestamp_ns": raw_timestamp,
            "device_ip": device_ip,
            "message_type": "update",
            "prefix": prefix_path,
            "alias": notification.alias,
            "atomic": notification.atomic,
            "updates": [],
            "deletes": [],
            "raw_notification": self._safe_message_to_dict(notification)
        }
        
        print(f"📝 处理 {len(notification.update)} 个更新项...")
        for i, update in enumerate(notification.update):
            print(f"\n  📝 更新项 #{i+1}:")
            # print_raw_message(update, f"Update #{i+1}") # Uncomment for full raw update debugging
            
            path_str = path_to_string(update.path)
            processed_value = None # Changed variable name to avoid confusion with gnmi_base_pb2.Value

            # --- MODIFICATION START ---
            # Based on your .proto, Update.value is a "Value" message, not a "TypedValue"
            # And Value.value is the bytes field with the actual data.
            if update.HasField("value"):
                value_message = update.value # This is the gnmi_base_pb2.Value object
                
                print(f"    检测到 Update.value (类型: gnmi.Value) 字段。")
                print(f"    Value 消息中的编码类型: {value_message.type} ({gnmi_base_pb2.Encoding.Name(value_message.type)})")

                # The actual data is in value_message.value (which is bytes)
                raw_data_bytes = value_message.value
                
                if raw_data_bytes:
                    print(f"    Value.value 原始数据字节长度: {len(raw_data_bytes)}")
                    # Now, process these bytes based on the encoding type if possible,
                    # otherwise try general decoding and JSON parsing.
                    
                    # Prioritize decoding based on specified encoding
                    decoded_string_for_parsing = None
                    if value_message.type == gnmi_base_pb2.Encoding.JSON or \
                       value_message.type == gnmi_base_pb2.Encoding.JSON_IETF:
                        print(f"    编码类型指示为 JSON/JSON_IETF。尝试直接解码为UTF-8并解析JSON。")
                        decoded_info = bytes_to_readable_string(raw_data_bytes)
                        decoded_string_for_parsing = decoded_info.get("utf8") or \
                                                     decoded_info.get("latin1") or \
                                                     decoded_info.get("utf8_replace")
                        if decoded_string_for_parsing:
                            parsed = try_parse_json(decoded_string_for_parsing)
                            if isinstance(parsed, (dict, list)):
                                processed_value = parsed
                            else:
                                processed_value = {"decoded_value": parsed, "original_encoding": gnmi_base_pb2.Encoding.Name(value_message.type)}
                        else:
                            processed_value = {"error": "Failed to decode JSON bytes", "raw_bytes_info": decoded_info, "original_encoding": gnmi_base_pb2.Encoding.Name(value_message.type)}
                            
                    elif value_message.type == gnmi_base_pb2.Encoding.BYTES:
                        print(f"    编码类型指示为 BYTES。尝试作为通用字节数据处理。")
                        decoded_info = bytes_to_readable_string(raw_data_bytes)
                        # For BYTES, we can't assume JSON, but we can try.
                        string_from_bytes = decoded_info.get("utf8") or decoded_info.get("latin1") or decoded_info.get("utf8_replace")
                        if string_from_bytes:
                            # Still attempt JSON parsing as some devices misuse BYTES for JSON
                            parsed_as_json = try_parse_json(string_from_bytes)
                            if isinstance(parsed_as_json, (dict, list)):
                                processed_value = parsed_as_json
                            else:
                                processed_value = {"decoded_value": string_from_bytes, "raw_bytes_info": decoded_info, "original_encoding": gnmi_base_pb2.Encoding.Name(value_message.type)}
                        else:
                            processed_value = {"raw_bytes_info": decoded_info, "original_encoding": gnmi_base_pb2.Encoding.Name(value_message.type)}

                    elif value_message.type == gnmi_base_pb2.Encoding.ASCII:
                        print(f"    编码类型指示为 ASCII。尝试解码为ASCII字符串。")
                        try:
                            processed_value = raw_data_bytes.decode('ascii')
                        except UnicodeDecodeError:
                            processed_value = {"error": "Failed to decode ASCII bytes", "raw_bytes_info": bytes_to_readable_string(raw_data_bytes), "original_encoding": gnmi_base_pb2.Encoding.Name(value_message.type)}
                        
                    elif value_message.type == gnmi_base_pb2.Encoding.PROTO:
                        print(f"    编码类型指示为 PROTO。尝试作为原始Protobuf字节处理 (需要额外定义)。")
                        # If you expect a specific Protobuf message here, you'd try to parse it.
                        # For now, just store raw bytes info.
                        processed_value = {"protobuf_bytes": bytes_to_readable_string(raw_data_bytes), "original_encoding": gnmi_base_pb2.Encoding.Name(value_message.type)}

                    else:
                        print(f"    未知编码类型: {value_message.type}。尝试通用字节处理。")
                        decoded_info = bytes_to_readable_string(raw_data_bytes)
                        string_from_bytes = decoded_info.get("utf8") or decoded_info.get("latin1") or decoded_info.get("utf8_replace")
                        if string_from_bytes:
                            parsed_as_json = try_parse_json(string_from_bytes)
                            if isinstance(parsed_as_json, (dict, list)):
                                processed_value = parsed_as_json
                            else:
                                processed_value = {"decoded_value": string_from_bytes, "raw_bytes_info": decoded_info, "original_encoding": gnmi_base_pb2.Encoding.Name(value_message.type)}
                        else:
                            processed_value = {"raw_bytes_info": decoded_info, "original_encoding": gnmi_base_pb2.Encoding.Name(value_message.type)}
                else:
                    print(f"    Value.value 字段为空。")
                    processed_value = {"error": "Value.value field is empty", "original_encoding": gnmi_base_pb2.Encoding.Name(value_message.type)}

            else:
                print(f"    Warning: Update 消息中未找到 'value' (gnmi.Value) 字段。")
                processed_value = {"error": "Update.value field not set", "raw_update_content": self._safe_message_to_dict(update)}
            # --- MODIFICATION END ---

            update_item = {
                "path": path_str,
                "value": processed_value, # Use the correctly processed value
                "raw_update": self._safe_message_to_dict(update)
            }
            data_to_log["updates"].append(update_item)
            
            print(f"    ✅ 路径: {path_str}")
            try:
                printable_value = json.dumps(processed_value, ensure_ascii=False, indent=2)
            except TypeError:
                printable_value = str(processed_value)
            print(f"    ✅ 值: {printable_value}")
        
        if notification.delete:
            print(f"🗑️  处理 {len(notification.delete)} 个删除项...")
            for i, delete_path in enumerate(notification.delete):
                path_str = path_to_string(delete_path)
                delete_item = {
                    "path": path_str,
                    "raw_delete": self._safe_message_to_dict(delete_path)
                }
                data_to_log["deletes"].append(delete_item)
                print(f"  🗑️  删除项 #{i+1}: {path_str}")
        
        self._save_to_file(device_ip, data_to_log)
        
        print(f"✅ 更新消息处理完成: {len(data_to_log['updates'])} 个更新, {len(data_to_log['deletes'])} 个删除")

    def _handle_sync_response(self, sync_response, device_ip, message_count):
        """处理同步响应消息"""
        print(f"🔄 处理同步响应消息...")
        sync_data = {
            "message_number": message_count,
            "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f'),
            "device_ip": device_ip,
            "message_type": "sync_response",
            "raw_sync_response": self._safe_message_to_dict(sync_response)
        }
        self._save_to_file(device_ip, sync_data)
        print(f"✅ 同步响应处理完成")

    def _handle_error(self, error, device_ip, message_count):
        """处理错误消息"""
        print(f"❌ 处理错误消息...")
        error_data = {
            "message_number": message_count,
            "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f'),
            "device_ip": device_ip,
            "message_type": "error",
            "error_code": error.code,
            "error_message": error.message,
            "raw_error": self._safe_message_to_dict(error)
        }
        self._save_to_file(device_ip, error_data)
        print(f"❌ 错误消息处理完成: {error.message}")

    def _safe_message_to_dict(self, message):
        """安全地将protobuf消息转换为字典"""
        try:
            return json_format.MessageToDict(message, preserving_proto_field_name=True)
        except Exception as e:
            return {"conversion_error": f"无法转换为字典: {e}", "string_repr": str(message)}

    def _save_to_file(self, device_ip, data):
        """保存数据到文件"""
        try:
            with device_data_locks[device_ip]:
                json_str = json.dumps(data, ensure_ascii=False, indent=None)
                device_data_files[device_ip].write(json_str + "\n")
                device_data_files[device_ip].flush()
            print(f"💾 数据已保存到文件 (设备: {device_ip})")
        except Exception as e:
            print(f"❌ 保存文件失败 (设备: {device_ip}): {e}")

def serve():
    """启动gRPC服务器"""
    os.environ['GRPC_VERBOSITY'] = 'INFO'
    server_address = '0.0.0.0:50051'
    server = grpc.server(concurrent.futures.ThreadPoolExecutor(max_workers=20))
    gnmi_dialout_pb2_grpc.add_gNMIDialoutServicer_to_server(TelemetryServicer(), server)
    server.add_insecure_port(server_address)
    server.start()
    
    print(f"🚀 gNMI Dialout服务器启动成功!")
    print(f"📡 监听地址: {server_address}")
    print(f"📂 数据目录: {DATA_DIR}")
    print(f"⏰ 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"{'='*60}")
    print(f"等待设备连接...")
    
    try:
        while True:
            time.sleep(86400)
    except KeyboardInterrupt:
        print(f"\n🛑 收到中断信号，服务器关闭中...")
        server.stop(grace=5)
        for device_ip, file_handle in device_data_files.items():
            try:
                file_handle.close()
                print(f"📁 已关闭设备 {device_ip} 的数据文件")
            except Exception as e:
                print(f"❌ 关闭设备 {device_ip} 文件失败: {e}")
        print(f"✅ 服务器已安全关闭")

if __name__ == '__main__':
    serve()