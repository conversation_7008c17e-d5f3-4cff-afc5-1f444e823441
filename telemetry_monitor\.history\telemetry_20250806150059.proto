syntax = "proto3";

package telemetry;

service gRPCDataservice {
    rpc telemetrySubscribe(TelemetrySubscriptionRequest) returns (stream TelemetryDataResponse);
}

message TelemetrySubscriptionRequest {
    string subscription_id = 1;
    string encoding = 2;
}

message TelemetryDataResponse {
    string node_id = 1;
    string subscription_id = 2;
    string sensor_path = 3;
    string timestamp = 4;
    bytes data = 5;
}
