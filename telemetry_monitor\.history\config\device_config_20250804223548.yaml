# 设备连接配置示例
devices:
  # 示例设备 1
  - name: "router-1"
    host: "***********"
    port: 830
    username: "admin"
    password: "password"
    # 可选：SSH 密钥认证
    # ssh_key_file: "/path/to/private/key"
    # 连接超时设置
    timeout: 30
    # 设备类型（可选，用于特定优化）
    device_type: "generic"
    
  # 示例设备 2
  - name: "switch-1"
    host: "***********"
    port: 830
    username: "admin"
    password: "password"
    timeout: 30
    device_type: "generic"

# 全局连接设置
connection_settings:
  # 默认超时时间（秒）
  default_timeout: 30
  # 重试次数
  retry_count: 3
  # 重试间隔（秒）
  retry_interval: 5
  # 是否验证 SSL 证书
  verify_ssl: false
  # NETCONF 会话保持时间（秒）
  session_keepalive: 60