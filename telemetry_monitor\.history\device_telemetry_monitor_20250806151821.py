import grpc
import json
import threading
import logging
import signal
import os
import time
from datetime import datetime
from queue import Queue
from contextlib import contextmanager
from concurrent import futures
from google.protobuf.json_format import MessageToDict

# Import generated gRPC code
import telemetry_service_pb2 as telemetry_pb2
import telemetry_service_pb2_grpc as telemetry_pb2_grpc

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Global flag for graceful shutdown
running = True

# Signal handler for graceful shutdown
def signal_handler(signum, frame):
    global running
    logger.info("Received shutdown signal, stopping threads...")
    running = False

signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)

# Buffer for batch writing
class TelemetryBuffer:
    def __init__(self, filename, max_buffer_size=100):
        self.filename = filename
        self.buffer = []
        self.max_buffer_size = max_buffer_size
        self.lock = threading.Lock()
        
    def add(self, data):
        with self.lock:
            self.buffer.append(data)
            if len(self.buffer) >= self.max_buffer_size:
                self.flush()
                
    def flush(self):
        with self.lock:
            if self.buffer:
                with open(self.filename, "a") as f:
                    for data in self.buffer:
                        f.write(json.dumps(data) + "\n")
                self.buffer.clear()

@contextmanager
def grpc_channel_manager(address, port, timeout=5):
    """Context manager for gRPC channel with reconnection logic"""
    channel = None
    try:
        channel = grpc.insecure_channel(f"{address}:{port}")
        yield channel
    except Exception as e:
        logger.error(f"Channel error: {e}")
        raise
    finally:
        if channel:
            channel.close()

# Define a function to handle telemetry data for a single device
class TelemetryServicer(telemetry_pb2_grpc.TelemetryServiceServicer):
    def __init__(self, buffer, device_address):
        self.buffer = buffer
        self.device_address = device_address
        self.running = True
        
    def telemetrySubscribe(self, request, context):
        logger.info(f"Received telemetry subscription from {self.device_address}")
        while self.running:
            try:
                # Wait for telemetry data
                time.sleep(1)  # Simulate receiving data
                yield telemetry_pb2.TelemetryUpdate(
                    node_id=self.device_address,
                    subscription_id=request.subscription_id,
                    sensor_path=request.sensor_paths[0],
                    timestamp=int(time.time() * 1000),
                    payload=b"sample data"
                )
            except Exception as e:
                logger.error(f"Error processing telemetry: {e}")
                break
                
    def stop(self):
        self.running = False

def monitor_device(device_config):
    """
    Starts a gRPC server to receive telemetry data from the device.

    :param device_config: Dictionary containing device configuration
    """
    device_address = device_config["address"]
    listen_address = device_config["server_address"]
    listen_port = device_config["port"]
    output_file = device_config["output_file"]
    
    logger.info(f"Starting telemetry server for device {device_address}")
    buffer = TelemetryBuffer(output_file)
    
    # Create the gRPC server
    server = grpc.server(futures.ThreadPoolExecutor(max_workers=10))
    servicer = TelemetryServicer(buffer, device_address)
    telemetry_pb2_grpc.add_TelemetryServiceServicer_to_server(servicer, server)
    
    # Start server
    server_addr = f"{listen_address}:{listen_port}"
    server.add_insecure_port(server_addr)
    server.start()
    
    logger.info(f"Starting monitoring for device {device_address}")
    buffer = TelemetryBuffer(output_file)
    retry_count = 0
    max_retries = 3

    # Wait for telemetry data to arrive
    try:
        logger.info(f"Telemetry server listening on {listen_address}:{listen_port}")
        while True:
            time.sleep(1)
            if not running:
                break
    finally:
        # Stop the server gracefully
        servicer.stop()
        server.stop(0)
        logger.info("Telemetry server stopped")    # Final flush of any remaining data
    buffer.flush()
    logger.info(f"Stopped monitoring device {device_address}")

# List of devices to monitor
SENSOR_PATHS = [
    "/openconfig-terminal-device:terminal-device/logical-channels/channel/ethernet/state",
    "/openconfig-platform:components/component/state",
    "/openconfig-terminal-device:terminal-device/logical-channels/channel/otn/state",
    "/openconfig-platform:components/component/openconfig-platform-transceiver:transceiver/physical-channels/channel/state",
    "/openconfig-platform:components/component/openconfig-terminal-device:optical-channel/state",
    "/openconfig-platform:components/component/openconfig-platform-transceiver:transceiver/state",
    "/openconfig-platform:components/component/cpu/openconfig-platform-cpu:utilization"
]

devices = [
    {
        "address": "***************",         # Device sending telemetry
        "server_address": "0.0.0.0",          # Listen on all interfaces
        "port": 50051,                        # gRPC server port for listening
        "output_file": "telemetry_data/device_***************.json",
        "subscription_id": "allPsg",
        "sample_interval": 1000,
        "heartbeat_interval": 15000,
        "sensor_paths": SENSOR_PATHS
    }
]

# Create telemetry_data directory if it doesn't exist
os.makedirs("telemetry_data", exist_ok=True)

# Create and start a thread for each device
threads = []
for device in devices:
    t = threading.Thread(
        target=monitor_device,
        args=(device,)  # Pass the entire device configuration
    )
    threads.append(t)
    t.start()

# Wait for all threads to complete
try:
    for t in threads:
        t.join()
except KeyboardInterrupt:
    logger.info("Received keyboard interrupt, initiating shutdown...")
    running = False
    # Wait again for threads to finish
    for t in threads:
        t.join()

logger.info("All monitoring threads have stopped. Exiting...")
