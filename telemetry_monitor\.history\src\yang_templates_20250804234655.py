#!/usr/bin/env python3
"""
OpenConfig Telemetry YANG 模型 XML 模板
提供标准的 OpenConfig Telemetry 配置模板
"""

from typing import Dict, List, Any, Optional
from string import Template
import logging


class YangTemplates:
    """OpenConfig Telemetry YANG 模板管理器"""
    
    # OpenConfig 命名空间定义
    NAMESPACES = {
        'telemetry': 'http://openconfig.net/yang/telemetry',
        'system': 'http://openconfig.net/yang/system',
        'interfaces': 'http://openconfig.net/yang/interfaces',
        'bgp': 'http://openconfig.net/yang/bgp',
        'ospf': 'http://openconfig.net/yang/ospfv2',
        'isis': 'http://openconfig.net/yang/isis',
        'platform': 'http://openconfig.net/yang/platform',
        'qos': 'http://openconfig.net/yang/qos',
        'mpls': 'http://openconfig.net/yang/mpls'
    }
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self._init_templates()
    
    def _init_templates(self):
        """初始化所有模板"""
        self.sensor_paths = self._init_sensor_paths()
        self.config_templates = self._init_config_templates()
    
    def _init_sensor_paths(self) -> Dict[str, Dict[str, List[str]]]:
        """初始化传感器路径定义"""
        return {
            # 系统监控路径
            'system': {
                'cpu_utilization': [
                    '/openconfig-system:system/cpus/cpu/state/total',
                    '/openconfig-system:system/cpus/cpu/state/user',
                    '/openconfig-system:system/cpus/cpu/state/kernel',
                    '/openconfig-system:system/cpus/cpu/state/idle'
                ],
                'cpu_detailed': [
                    '/openconfig-system:system/cpus/cpu/state',
                    '/openconfig-system:system/processes/process/state/cpu-usage-system',
                    '/openconfig-system:system/processes/process/state/cpu-usage-user'
                ],
                'memory_utilization': [
                    '/openconfig-system:system/memory/state/physical',
                    '/openconfig-system:system/memory/state/reserved'
                ],
                'memory_detailed': [
                    '/openconfig-system:system/memory/state',
                    '/openconfig-system:system/processes/process/state/memory-usage',
                    '/openconfig-system:system/processes/process/state/memory-utilization'
                ],
                'storage_utilization': [
                    '/openconfig-system:system/storage/filesystems/filesystem/state/size',
                    '/openconfig-system:system/storage/filesystems/filesystem/state/used',
                    '/openconfig-system:system/storage/filesystems/filesystem/state/available'
                ],
                'system_temperature': [
                    '/openconfig-platform:components/component/state/temperature/instant',
                    '/openconfig-platform:components/component/state/temperature/avg',
                    '/openconfig-platform:components/component/state/temperature/max'
                ],
                'process_stats': [
                    '/openconfig-system:system/processes/process/state/pid',
                    '/openconfig-system:system/processes/process/state/name',
                    '/openconfig-system:system/processes/process/state/args',
                    '/openconfig-system:system/processes/process/state/start-time'
                ]
            },
            
            # 接口监控路径
            'interface': {
                'interface_traffic': [
                    '/openconfig-interfaces:interfaces/interface/state/counters/in-octets',
                    '/openconfig-interfaces:interfaces/interface/state/counters/out-octets',
                    '/openconfig-interfaces:interfaces/interface/state/counters/in-pkts',
                    '/openconfig-interfaces:interfaces/interface/state/counters/out-pkts'
                ],
                'interface_status': [
                    '/openconfig-interfaces:interfaces/interface/state/admin-status',
                    '/openconfig-interfaces:interfaces/interface/state/oper-status',
                    '/openconfig-interfaces:interfaces/interface/state/last-change'
                ],
                'interface_errors': [
                    '/openconfig-interfaces:interfaces/interface/state/counters/in-errors',
                    '/openconfig-interfaces:interfaces/interface/state/counters/out-errors',
                    '/openconfig-interfaces:interfaces/interface/state/counters/in-discards',
                    '/openconfig-interfaces:interfaces/interface/state/counters/out-discards'
                ],
                'interface_detailed_stats': [
                    '/openconfig-interfaces:interfaces/interface/state/counters',
                    '/openconfig-interfaces:interfaces/interface/ethernet/state/counters'
                ],
                'interface_queue_stats': [
                    '/openconfig-qos:qos/interfaces/interface/output/queues/queue/state/transmit-pkts',
                    '/openconfig-qos:qos/interfaces/interface/output/queues/queue/state/transmit-octets',
                    '/openconfig-qos:qos/interfaces/interface/output/queues/queue/state/dropped-pkts'
                ],
                'optical_stats': [
                    '/openconfig-platform:components/component/optical-channel/state/output-power/instant',
                    '/openconfig-platform:components/component/optical-channel/state/input-power/instant',
                    '/openconfig-platform:components/component/transceiver/state/output-power/instant',
                    '/openconfig-platform:components/component/transceiver/state/input-power/instant'
                ]
            },
            
            # 路由协议监控路径
            'routing': {
                'bgp_neighbors': [
                    '/openconfig-bgp:bgp/neighbors/neighbor/state/session-state',
                    '/openconfig-bgp:bgp/neighbors/neighbor/state/established-transitions',
                    '/openconfig-bgp:bgp/neighbors/neighbor/afi-safis/afi-safi/state/prefixes/received',
                    '/openconfig-bgp:bgp/neighbors/neighbor/afi-safis/afi-safi/state/prefixes/sent'
                ],
                'ospf_neighbors': [
                    '/openconfig-ospfv2:ospfv2/areas/area/interfaces/interface/neighbors/neighbor/state/neighbor-id',
                    '/openconfig-ospfv2:ospfv2/areas/area/interfaces/interface/neighbors/neighbor/state/state',
                    '/openconfig-ospfv2:ospfv2/areas/area/interfaces/interface/neighbors/neighbor/state/dead-time'
                ],
                'isis_adjacency': [
                    '/openconfig-isis:isis/interfaces/interface/levels/level/adjacencies/adjacency/state/adjacency-state',
                    '/openconfig-isis:isis/interfaces/interface/levels/level/adjacencies/adjacency/state/neighbor-ipv4-address',
                    '/openconfig-isis:isis/interfaces/interface/levels/level/adjacencies/adjacency/state/remaining-hold-time'
                ],
                'routing_table_stats': [
                    '/openconfig-network-instance:network-instances/network-instance/protocols/protocol/state/counters/in-pkts',
                    '/openconfig-network-instance:network-instances/network-instance/protocols/protocol/state/counters/out-pkts'
                ]
            },
            
            # 硬件监控路径
            'hardware': {
                'fan_status': [
                    '/openconfig-platform:components/component/fan/state/speed',
                    '/openconfig-platform:components/component/state/oper-status'
                ],
                'power_supply': [
                    '/openconfig-platform:components/component/power-supply/state/output-power',
                    '/openconfig-platform:components/component/power-supply/state/input-current',
                    '/openconfig-platform:components/component/power-supply/state/output-voltage'
                ],
                'temperature_sensors': [
                    '/openconfig-platform:components/component/state/temperature/instant',
                    '/openconfig-platform:components/component/state/temperature/alarm-status',
                    '/openconfig-platform:components/component/state/temperature/alarm-threshold'
                ],
                'hardware_alarms': [
                    '/openconfig-platform:components/component/state/oper-status',
                    '/openconfig-platform:components/component/state/empty',
                    '/openconfig-platform:components/component/state/removable'
                ]
            },
            
            # QoS 监控路径
            'qos': {
                'queue_statistics': [
                    '/openconfig-qos:qos/interfaces/interface/output/queues/queue/state/transmit-pkts',
                    '/openconfig-qos:qos/interfaces/interface/output/queues/queue/state/transmit-octets',
                    '/openconfig-qos:qos/interfaces/interface/output/queues/queue/state/dropped-pkts',
                    '/openconfig-qos:qos/interfaces/interface/output/queues/queue/state/name'
                ],
                'policy_statistics': [
                    '/openconfig-qos:qos/interfaces/interface/output/classifiers/classifier/terms/term/actions/state/target-group',
                    '/openconfig-qos:qos/interfaces/interface/output/classifiers/classifier/terms/term/state/matched-pkts',
                    '/openconfig-qos:qos/interfaces/interface/output/classifiers/classifier/terms/term/state/matched-octets'
                ],
                'traffic_classification': [
                    '/openconfig-qos:qos/classifiers/classifier/terms/term/conditions/ipv4/state/dscp-set',
                    '/openconfig-qos:qos/classifiers/classifier/terms/term/conditions/ipv4/state/protocol',
                    '/openconfig-qos:qos/classifiers/classifier/terms/term/conditions/transport/state/source-port'
                ],
                'bandwidth_utilization': [
                    '/openconfig-qos:qos/interfaces/interface/output/scheduler-policy/schedulers/scheduler/state/priority',
                    '/openconfig-qos:qos/interfaces/interface/output/scheduler-policy/schedulers/scheduler/inputs/input/state/weight'
                ]
            },
            
            # MPLS/VPN 监控路径
            'mpls': {
                'lsp_statistics': [
                    '/openconfig-mpls:mpls/lsps/constrained-path/tunnels/tunnel/state/oper-status',
                    '/openconfig-mpls:mpls/lsps/constrained-path/tunnels/tunnel/state/counters/bytes',
                    '/openconfig-mpls:mpls/lsps/constrained-path/tunnels/tunnel/state/counters/packets'
                ],
                'vrf_statistics': [
                    '/openconfig-network-instance:network-instances/network-instance/state/name',
                    '/openconfig-network-instance:network-instances/network-instance/state/type',
                    '/openconfig-network-instance:network-instances/network-instance/protocols/protocol/state/identifier'
                ],
                'mpls_labels': [
                    '/openconfig-mpls:mpls/global/reserved-label-blocks/reserved-label-block/state/lower-bound',
                    '/openconfig-mpls:mpls/global/reserved-label-blocks/reserved-label-block/state/upper-bound',
                    '/openconfig-mpls:mpls/global/interface-attributes/interface/state/mpls-enabled'
                ],
                'vpn_connections': [
                    '/openconfig-network-instance:network-instances/network-instance/inter-instance-policies/apply-policy/state/import-policy',
                    '/openconfig-network-instance:network-instances/network-instance/inter-instance-policies/apply-policy/state/export-policy'
                ]
            }
        }
    
    def _init_config_templates(self) -> Dict[str, Template]:
        """初始化配置模板"""
        return {
            # Telemetry 系统配置模板
            'telemetry_system': Template('''
<config xmlns="urn:ietf:params:xml:ns:netconf:base:1.0">
  <telemetry-system xmlns="http://openconfig.net/yang/telemetry">
    <sensor-groups>
      $sensor_groups
    </sensor-groups>
    <destination-groups>
      $destination_groups
    </destination-groups>
    <subscriptions>
      $subscriptions
    </subscriptions>
  </telemetry-system>
</config>
            '''),
            
            # Sensor Group 模板
            'sensor_group': Template('''
      <sensor-group>
        <sensor-group-id>$group_id</sensor-group-id>
        <config>
          <sensor-group-id>$group_id</sensor-group-id>
        </config>
        <sensor-paths>
          $sensor_paths
        </sensor-paths>
      </sensor-group>
            '''),
            
            # Sensor Path 模板
            'sensor_path': Template('''
          <sensor-path>
            <path>$path</path>
            <config>
              <path>$path</path>
            </config>
          </sensor-path>
            '''),
            
            # Destination Group 模板
            'destination_group': Template('''
      <destination-group>
        <group-id>$group_id</group-id>
        <config>
          <group-id>$group_id</group-id>
        </config>
        <destinations>
          $destinations
        </destinations>
      </destination-group>
            '''),
            
            # Destination 模板
            'destination': Template('''
          <destination>
            <destination-address>$address</destination-address>
            <destination-port>$port</destination-port>
            <config>
              <destination-address>$address</destination-address>
              <destination-port>$port</destination-port>
            </config>
          </destination>
            '''),
            
            # Subscription 模板
            'subscription': Template('''
      <subscription>
        <subscription-id>$subscription_id</subscription-id>
        <config>
          <subscription-id>$subscription_id</subscription-id>
          <local-source-address>$source_address</local-source-address>
          <originated-qos-marking>$qos_marking</originated-qos-marking>
        </config>
        <sensor-profiles>
          <sensor-profile>
            <sensor-group>$sensor_group</sensor-group>
            <config>
              <sensor-group>$sensor_group</sensor-group>
              <sample-interval>$sample_interval</sample-interval>
              <suppress-redundant>$suppress_redundant</suppress-redundant>
              <heartbeat-interval>$heartbeat_interval</heartbeat-interval>
            </config>
          </sensor-profile>
        </sensor-profiles>
        <destination-groups>
          <destination-group>$destination_group</destination-group>
        </destination-groups>
      </subscription>
            ''')
        }
    
    def get_sensor_paths(self, category: str, metric: str) -> List[str]:
        """
        获取指定类别和指标的传感器路径
        
        Args:
            category: 监控类别 (system, interface, routing, hardware, qos, mpls)
            metric: 具体指标名称
            
        Returns:
            传感器路径列表
        """
        if category not in self.sensor_paths:
            raise ValueError(f"不支持的监控类别: {category}")
        
        if metric not in self.sensor_paths[category]:
            raise ValueError(f"类别 '{category}' 中不存在指标: {metric}")
        
        return self.sensor_paths[category][metric]
    
    def get_all_paths_for_category(self, category: str) -> Dict[str, List[str]]:
        """
        获取指定类别的所有传感器路径
        
        Args:
            category: 监控类别
            
        Returns:
            指标名称到路径列表的映射
        """
        if category not in self.sensor_paths:
            raise ValueError(f"不支持的监控类别: {category}")
        
        return self.sensor_paths[category]
    
    def generate_sensor_group_xml(self, group_id: str, paths: List[str]) -> str:
        """
        生成 Sensor Group 配置 XML
        
        Args:
            group_id: 传感器组 ID
            paths: 传感器路径列表
            
        Returns:
            Sensor Group XML 配置
        """
        sensor_paths_xml = []
        for path in paths:
            sensor_paths_xml.append(
                self.config_templates['sensor_path'].substitute(path=path)
            )
        
        return self.config_templates['sensor_group'].substitute(
            group_id=group_id,
            sensor_paths='\n'.join(sensor_paths_xml)
        )
    
    def generate_destination_group_xml(self, group_id: str, destinations: List[Dict[str, Any]]) -> str:
        """
        生成 Destination Group 配置 XML
        
        Args:
            group_id: 目标组 ID
            destinations: 目标配置列表
            
        Returns:
            Destination Group XML 配置
        """
        destinations_xml = []
        for dest in destinations:
            destinations_xml.append(
                self.config_templates['destination'].substitute(
                    address=dest.get('address', ''),
                    port=dest.get('port', 57400),
                    protocol=dest.get('protocol', 'grpc'),
                    encoding=dest.get('encoding', 'json')
                )
            )
        
        return self.config_templates['destination_group'].substitute(
            group_id=group_id,
            destinations='\n'.join(destinations_xml)
        )
    
    def generate_subscription_xml(self, subscription_config: Dict[str, Any]) -> str:
        """
        生成 Subscription 配置 XML
        
        Args:
            subscription_config: 订阅配置字典
            
        Returns:
            Subscription XML 配置
        """
        return self.config_templates['subscription'].substitute(
            subscription_id=subscription_config.get('subscription_id', ''),
            sensor_group=subscription_config.get('sensor_group', ''),
            destination_group=subscription_config.get('destination_group', ''),
            sample_interval=subscription_config.get('sample_interval', 30000),
            source_address=subscription_config.get('source_address', ''),
            qos_marking=subscription_config.get('qos_marking', 0),
            suppress_redundant=subscription_config.get('suppress_redundant', 'false'),
            heartbeat_interval=subscription_config.get('heartbeat_interval', 60000)
        )
    
    def generate_complete_telemetry_config(self, 
                                         sensor_groups: List[Dict[str, Any]],
                                         destination_groups: List[Dict[str, Any]],
                                         subscriptions: List[Dict[str, Any]]) -> str:
        """
        生成完整的 Telemetry 配置 XML
        
        Args:
            sensor_groups: 传感器组配置列表
            destination_groups: 目标组配置列表
            subscriptions: 订阅配置列表
            
        Returns:
            完整的 Telemetry 配置 XML
        """
        # 生成 Sensor Groups XML
        sensor_groups_xml = []
        for sg in sensor_groups:
            sensor_groups_xml.append(
                self.generate_sensor_group_xml(sg['group_id'], sg['paths'])
            )
        
        # 生成 Destination Groups XML
        destination_groups_xml = []
        for dg in destination_groups:
            destination_groups_xml.append(
                self.generate_destination_group_xml(dg['group_id'], dg['destinations'])
            )
        
        # 生成 Subscriptions XML
        subscriptions_xml = []
        for sub in subscriptions:
            subscriptions_xml.append(
                self.generate_subscription_xml(sub)
            )
        
        # 组合完整配置
        return self.config_templates['telemetry_system'].substitute(
            sensor_groups='\n'.join(sensor_groups_xml),
            destination_groups='\n'.join(destination_groups_xml),
            subscriptions='\n'.join(subscriptions_xml)
        )
    
    def list_available_categories(self) -> List[str]:
        """获取所有可用的监控类别"""
        return list(self.sensor_paths.keys())
    
    def list_available_metrics(self, category: str) -> List[str]:
        """
        获取指定类别的所有可用指标
        
        Args:
            category: 监控类别
            
        Returns:
            指标名称列表
        """
        if category not in self.sensor_paths:
            raise ValueError(f"不支持的监控类别: {category}")
        
        return list(self.sensor_paths[category].keys())
    
    def validate_paths(self, paths: List[str]) -> Dict[str, bool]:
        """
        验证传感器路径的有效性
        
        Args:
            paths: 要验证的路径列表
            
        Returns:
            路径到验证结果的映射
        """
        results = {}
        all_paths = []
        
        # 收集所有已知路径
        for category in self.sensor_paths.values():
            for metric_paths in category.values():
                all_paths.extend(metric_paths)
        
        # 验证每个路径
        for path in paths:
            results[path] = path in all_paths
        
        return results