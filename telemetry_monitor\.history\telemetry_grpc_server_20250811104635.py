import grpc
import concurrent.futures
import json
import os
import threading
import time
from datetime import datetime
from google.protobuf import json_format
import binascii

# 导入生成的gNMI protobuf文件
from tmp_proto import gnmi_base_pb2
from tmp_proto import gnmi_base_pb2_grpc
from tmp_proto import gnmi_dialout_pb2
from tmp_proto import gnmi_dialout_pb2_grpc

# 存储每个设备的遥测数据文件句柄和锁
device_data_files = {}
device_data_locks = {}

# 数据存储目录
DATA_DIR = "telemetry_data"
# 订阅名称（Dialout消息通常不带订阅名，这里提供可配置默认值）
SUBSCRIPTION_NAME = os.environ.get("SUBSCRIPTION_NAME", "allPsg")

# 参考 telemetry_monitor_new.py 的监控路径集合（用于聚合到根路径键）
SENSOR_ROOTS = [
    "/openconfig-terminal-device:terminal-device/logical-channels/channel/otn/state",
    "/openconfig-terminal-device:terminal-device/logical-channels/channel/ethernet/state",
    "/openconfig-platform:components/component/openconfig-platform-transceiver:transceiver/physical-channels/channel/state",
    "/openconfig-platform:components/component/state",
    "/openconfig-platform:components/component/cpu/openconfig-platform-cpu:utilization",
    "/openconfig-platform:components/component/openconfig-terminal-device:optical-channel/state",
    "/openconfig-platform:components/component/openconfig-platform-transceiver:transceiver/state",
    "/openconfig-platform:components/component/openconfig-transport-line-common:optical-port/state",
]

os.makedirs(DATA_DIR, exist_ok=True)

def safe_decode_bytes(data):
    """
    安全解码字节数据，处理乱码问题
    """
    if isinstance(data, bytes):
        try:
            # 尝试UTF-8解码
            decoded = data.decode('utf-8')
            print(f"UTF-8解码成功: '{decoded}'")
            return decoded
        except UnicodeDecodeError:
            try:
                # 尝试latin-1解码
                decoded = data.decode('latin-1')
                print(f"Latin-1解码成功: '{decoded}'")
                return decoded
            except UnicodeDecodeError:
                # 如果都失败，使用错误处理方式
                decoded = data.decode('utf-8', errors='replace')
                print(f"UTF-8替换模式解码: '{decoded}'")
                return decoded
    return str(data)

def analyze_raw_bytes(data):
    """
    分析原始字节数据，查找可能的JSON内容
    """
    if not isinstance(data, bytes):
        return None

    print(f"📊 分析原始字节数据 (长度: {len(data)} bytes)")
    print(f"十六进制: {data.hex()}")

    # 尝试多种编码解析
    encodings = ['utf-8', 'latin-1', 'ascii', 'cp1252']
    decoded_results = {}

    for encoding in encodings:
        try:
            decoded = data.decode(encoding)
            decoded_results[encoding] = decoded
            print(f"{encoding} 解码: '{decoded}'")
        except UnicodeDecodeError as e:
            print(f"{encoding} 解码失败: {e}")

    return decoded_results

def extract_json_from_mixed_data(data_str):
    """
    从混合数据中提取JSON内容，处理protobuf混合情况
    """
    import re

    print(f"🔍 从混合数据中搜索JSON...")
    print(f"输入数据: '{data_str}' (长度: {len(data_str)})")

    # 查找所有可能的JSON片段 - 更宽松的正则表达式
    json_patterns = [
        r'(\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\})',  # 嵌套JSON对象
        r'(\{.*?\})',  # 简单JSON对象 - 非贪婪
        r'(\[.*?\])',  # JSON数组 - 非贪婪
    ]

    found_jsons = []

    for i, pattern in enumerate(json_patterns):
        matches = re.findall(pattern, data_str, re.DOTALL)
        print(f"模式 {i+1} 匹配到 {len(matches)} 个结果")

        for j, match in enumerate(matches):
            print(f"  匹配 {j+1}: '{match}'")
            try:
                parsed = json.loads(match)
                found_jsons.append({
                    'raw': match,
                    'parsed': parsed,
                    'pattern': i+1
                })
                print(f"  ✅ JSON解析成功: {type(parsed)}")
            except json.JSONDecodeError as e:
                print(f"  ❌ JSON解析失败: {e}")

    if found_jsons:
        print(f"🎯 总共找到 {len(found_jsons)} 个有效JSON")
        return found_jsons
    else:
        print(f"❌ 未找到有效JSON")
        return None

def clean_json_string(json_str):
    """
    清理JSON字符串，移除可能的前缀字符和格式问题
    """
    if not isinstance(json_str, str):
        return str(json_str)

    original_str = json_str
    print(f"原始字符串: '{original_str}' (长度: {len(original_str)})")

    # 显示字符的详细信息
    print("字符分析:")
    for i, char in enumerate(original_str[:50]):  # 只显示前50个字符
        if ord(char) < 32 or ord(char) > 126:  # 非可见字符
            print(f"  [{i}] '\\x{ord(char):02x}' ({ord(char)}) - 特殊字符")
        else:
            print(f"  [{i}] '{char}' ({ord(char)})")

    # 移除前后空白字符和控制字符
    import re
    json_str = re.sub(r'^[\s\x00-\x1f\x7f-\xff]*', '', json_str)  # 移除开头的空白和控制字符
    json_str = re.sub(r'[\s\x00-\x1f\x7f-\xff]*$', '', json_str)  # 移除结尾的空白和控制字符

def try_parse_json(data_str):
    """
    尝试多种方式解析JSON字符串
    """
    print(f"🔍 尝试解析JSON数据...")
    print(f"输入数据类型: {type(data_str)}")
    print(f"输入数据: '{data_str}'")

    if not isinstance(data_str, str):
        data_str = str(data_str)

    # 方法1：直接解析
    try:
        result = json.loads(data_str)
        print(f"✅ 直接JSON解析成功: {type(result)}")
        return result
    except json.JSONDecodeError as e:
        print(f"❌ 直接JSON解析失败: {e}")

    # 方法2：清理后解析
    try:
        cleaned_str = clean_json_string(data_str)
        result = json.loads(cleaned_str)
        print(f"✅ 清理后JSON解析成功: {type(result)}")
        return result
    except json.JSONDecodeError as e:
        print(f"❌ 清理后JSON解析失败: {e}")

    # 方法3：尝试修复常见问题
    try:
        # 替换可能的问题字符
        fixed_str = data_str

        # 移除控制字符
        import re
        fixed_str = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', fixed_str)

        # 查找并提取JSON部分
        json_match = re.search(r'(\{.*\}|\[.*\])', fixed_str, re.DOTALL)
        if json_match:
            json_part = json_match.group(1)
            print(f"正则提取的JSON部分: '{json_part}'")
            result = json.loads(json_part)
            print(f"✅ 正则修复后JSON解析成功: {type(result)}")
            return result
        else:
            print(f"❌ 正则未找到JSON结构")
    except json.JSONDecodeError as e:
        print(f"❌ 正则修复后JSON解析失败: {e}")
    except Exception as e:
        print(f"❌ 正则处理异常: {e}")

def bytes_to_readable_string(data):
    """
    将字节数据转换为可读字符串，包含十六进制表示
    """
    if isinstance(data, bytes):
        try:
            # 首先尝试UTF-8解码
            decoded = data.decode('utf-8')
            return {
                "decoded_utf8": decoded,
                "hex": binascii.hexlify(data).decode('ascii'),
                "length": len(data)
            }
        except UnicodeDecodeError:
            return {
                "decoded_replace": data.decode('utf-8', errors='replace'),
                "hex": binascii.hexlify(data).decode('ascii'),
                "length": len(data),
                "decode_error": True
            }
    return str(data)

def process_gnmi_value(value_obj):
    """
    处理gNMI Value消息 (包含bytes value和Encoding type)
    """
    print(f"🔍 处理gNMI Value对象...")
    print(f"Value对象类型: {type(value_obj)}")

    try:
        # 根据proto定义，Value包含bytes value和Encoding type
        raw_bytes = value_obj.value
        encoding_type = value_obj.type

        print(f"编码类型: {encoding_type}")
        print(f"原始数据长度: {len(raw_bytes)} bytes")

        # 额外分析原始字节
        if isinstance(raw_bytes, bytes):
            byte_analysis = analyze_raw_bytes(raw_bytes)
            print(f"字节分析结果: {byte_analysis}")

        encoding_map = {
            0: "JSON",
            1: "BYTES",
            2: "PROTO",
            3: "ASCII",
            4: "JSON_IETF"
        }
        encoding_name = encoding_map.get(encoding_type, f"UNKNOWN({encoding_type})")
        print(f"编码类型名称: {encoding_name}")

        # 优化：原始内容为空时直接返回 None 或空字符串
        if not raw_bytes or (isinstance(raw_bytes, bytes) and len(raw_bytes) == 0):
            print("⚠️ Value对象原始数据为空，直接返回空字符串")
            return ""

        if encoding_type in [0, 4]:  # JSON or JSON_IETF
            print(f"按{encoding_name}格式处理...")
            decoded_str = safe_decode_bytes(raw_bytes)
            if not decoded_str.strip():
                print("⚠️ 解码后内容为空字符串，直接返回空字符串")
                return ""
            return try_parse_json(decoded_str)

        elif encoding_type == 3:  # ASCII
            print(f"按ASCII格式处理...")
            decoded_str = safe_decode_bytes(raw_bytes)
            if not decoded_str.strip():
                print("⚠️ ASCII内容为空字符串，直接返回空字符串")
                return ""
            parsed_json = try_parse_json(decoded_str)
            if not isinstance(parsed_json, (dict, list)):
                # 兜底：混合数据中提取 JSON
                try:
                    mixed = extract_json_from_mixed_data(decoded_str)
                    if mixed and len(mixed) > 0:
                        # 返回第一个解析成功的 JSON 片段
                        return mixed[0]['parsed']
                except Exception as e:
                    print(f"ASCII混合提取失败: {e}")
                return decoded_str
            return parsed_json

        elif encoding_type == 1:  # BYTES
            print(f"按BYTES格式处理...")
            decoded_str = safe_decode_bytes(raw_bytes)
            if not decoded_str.strip():
                print("⚠️ BYTES内容为空字符串，直接返回空字符串")
                return ""
            parsed_json = try_parse_json(decoded_str)
            if not isinstance(parsed_json, (dict, list)):
                # 兜底：混合数据中提取 JSON
                try:
                    mixed = extract_json_from_mixed_data(decoded_str)
                    if mixed and len(mixed) > 0:
                        print("BYTES混合数据中提取到JSON片段")
                        return mixed[0]['parsed']
                except Exception as e:
                    print(f"BYTES混合提取失败: {e}")
                print(f"BYTES数据不是JSON，返回字符串")
                return decoded_str
            print(f"BYTES数据成功解析为JSON")
            return parsed_json

        elif encoding_type == 2:  # PROTO
            print(f"按PROTO格式处理...")
            return {
                "proto_data": bytes_to_readable_string(raw_bytes),
                "note": "protobuf数据需要特定的解析器"
            }
        else:
            print(f"未知编码类型，尝试通用处理...")
            decoded_str = safe_decode_bytes(raw_bytes)
            if not decoded_str.strip():
                print("⚠️ 通用处理内容为空字符串，直接返回空字符串")
                return ""
            return try_parse_json(decoded_str)

    except Exception as e:
        print(f"❌ Value对象处理失败: {e}")
        return {
            "error": f"Value处理失败: {e}",
            "raw_object": str(value_obj)
        }

def get_json_value(typed_value):
    """
    从TypedValue中提取JSON值，增强错误处理和调试信息。
    """
    print(f"=== TypedValue 详细分析 ===")
    print(f"TypedValue 对象类型: {type(typed_value)}")

    # 安全地打印所有可用字段和其状态
    available_fields = []
    for field_descriptor in typed_value.DESCRIPTOR.fields:
        field_name = field_descriptor.name
        try:
            if field_descriptor.label == field_descriptor.LABEL_REPEATED:
                # 对于重复字段，检查是否有内容
                has_field = bool(getattr(typed_value, field_name))
            else:
                # 对于单一字段，使用HasField检查
                has_field = typed_value.HasField(field_name)

            if has_field:
                field_value = getattr(typed_value, field_name)
                available_fields.append(field_name)
                print(f"  ✓ {field_name}: {type(field_value)} = {field_value}")
            else:
                print(f"  ✗ {field_name}: (未设置)")
        except Exception as e:
            # 某些字段可能无法用HasField检查，尝试直接获取
            try:
                field_value = getattr(typed_value, field_name)
                if field_value is not None and field_value != "":
                    available_fields.append(field_name)
                    print(f"  ✓ {field_name}: {type(field_value)} = {field_value} (直接获取)")
                else:
                    print(f"  ✗ {field_name}: (空值)")
            except:
                print(f"  ? {field_name}: (无法访问: {e})")

    print(f"活跃字段: {available_fields}")

    # 定义一个安全的字段检查函数
    def safe_has_field(obj, field_name):
        try:
            return obj.HasField(field_name)
        except ValueError:
            # 字段不存在或不能用HasField检查
            return False
        except Exception:
            return False

    # 按优先级处理各种类型
    if safe_has_field(typed_value, "json_val"):
        print(f"处理 json_val 字段...")
        raw_data = typed_value.json_val
        decoded_info = bytes_to_readable_string(raw_data)
        print(f"json_val 原始数据: {decoded_info}")
        try:
            if isinstance(raw_data, bytes):
                decoded_str = safe_decode_bytes(raw_data)
                parsed_json = try_parse_json(decoded_str)
                print(f"json_val 解析结果类型: {type(parsed_json)}")
                print(f"json_val 解析结果内容: {json.dumps(parsed_json, ensure_ascii=False, indent=2) if isinstance(parsed_json, (dict, list)) else parsed_json}")
                return parsed_json
            else:
                parsed_json = try_parse_json(str(raw_data))
                print(f"json_val 解析结果: {parsed_json}")
                return parsed_json
        except json.JSONDecodeError as e:
            print(f"json_val JSON解析失败: {e}")
            return safe_decode_bytes(raw_data) if isinstance(raw_data, bytes) else str(raw_data)
        except Exception as e:
            print(f"json_val 处理异常: {e}")
            return decoded_info

    elif safe_has_field(typed_value, "json_ietf_val"):
        print(f"处理 json_ietf_val 字段...")
        raw_data = typed_value.json_ietf_val
        decoded_info = bytes_to_readable_string(raw_data)
        print(f"json_ietf_val 原始数据: {decoded_info}")
        try:
            if isinstance(raw_data, bytes):
                decoded_str = safe_decode_bytes(raw_data)
                parsed_json = try_parse_json(decoded_str)
                print(f"json_ietf_val 解析结果类型: {type(parsed_json)}")
                print(f"json_ietf_val 解析结果内容: {json.dumps(parsed_json, ensure_ascii=False, indent=2) if isinstance(parsed_json, (dict, list)) else parsed_json}")
                return parsed_json
            else:
                parsed_json = try_parse_json(str(raw_data))
                print(f"json_ietf_val 解析结果: {parsed_json}")
                return parsed_json
        except json.JSONDecodeError as e:
            print(f"json_ietf_val JSON解析失败: {e}")
            return safe_decode_bytes(raw_data) if isinstance(raw_data, bytes) else str(raw_data)
        except Exception as e:
            print(f"json_ietf_val 处理异常: {e}")
            return decoded_info

    elif safe_has_field(typed_value, "string_val"):
        print(f"处理 string_val: {typed_value.string_val}")
        return typed_value.string_val

    elif safe_has_field(typed_value, "int_val"):
        print(f"处理 int_val: {typed_value.int_val}")
        return typed_value.int_val

    elif safe_has_field(typed_value, "uint_val"):
        print(f"处理 uint_val: {typed_value.uint_val}")
        return typed_value.uint_val

    elif safe_has_field(typed_value, "bool_val"):
        print(f"处理 bool_val: {typed_value.bool_val}")
        return typed_value.bool_val

    elif safe_has_field(typed_value, "bytes_val"):
        print(f"处理 bytes_val 字段...")
        raw_data = typed_value.bytes_val
        decoded_info = bytes_to_readable_string(raw_data)
        print(f"bytes_val 原始数据: {decoded_info}")

        # 额外分析原始字节
        if isinstance(raw_data, bytes):
            byte_analysis = analyze_raw_bytes(raw_data)
            print(f"字节分析结果: {byte_analysis}")

        try:
            decoded_str = safe_decode_bytes(raw_data)
            # 尝试解析为JSON
            parsed_json = try_parse_json(decoded_str)
            if isinstance(parsed_json, (dict, list)):
                print(f"bytes_val 成功解析为JSON: {type(parsed_json)}")
                print(f"bytes_val JSON内容: {json.dumps(parsed_json, ensure_ascii=False, indent=2)}")
                return parsed_json
            else:
                print(f"bytes_val 不是有效JSON，返回解码字符串: '{parsed_json}'")
                return parsed_json
        except Exception as e:
            print(f"bytes_val 处理异常: {e}")
            return decoded_info

    elif safe_has_field(typed_value, "float_val"):
        print(f"处理 float_val: {typed_value.float_val}")
        return typed_value.float_val

    elif safe_has_field(typed_value, "double_val"):
        print(f"处理 double_val: {typed_value.double_val}")
        return typed_value.double_val

    elif safe_has_field(typed_value, "decimal_val"):
        decimal_data = {
            "digits": typed_value.decimal_val.digits,
            "precision": typed_value.decimal_val.precision
        }
        print(f"处理 decimal_val: {decimal_data}")
        return decimal_data

    elif safe_has_field(typed_value, "leaflist_val"):
        print(f"处理 leaflist_val，包含 {len(typed_value.leaflist_val.element)} 个元素")
        leaflist_data = []
        for i, elem in enumerate(typed_value.leaflist_val.element):
            print(f"  处理 leaflist 元素 {i}:")
            elem_value = get_json_value(elem)
            leaflist_data.append(elem_value)
        print(f"leaflist_val 完整结果: {leaflist_data}")
        return leaflist_data

    elif safe_has_field(typed_value, "any_val"):
        print(f"处理 any_val 字段...")
        any_val = typed_value.any_val
        print(f"any_val type_url: {any_val.type_url}")
        raw_data = any_val.value
        decoded_info = bytes_to_readable_string(raw_data)
        print(f"any_val 原始数据: {decoded_info}")

        try:
            decoded_str = safe_decode_bytes(raw_data)
            parsed_json = try_parse_json(decoded_str)
            if isinstance(parsed_json, (dict, list)):
                print(f"any_val 成功解析为JSON: {type(parsed_json)}")
                print(f"any_val JSON内容: {json.dumps(parsed_json, ensure_ascii=False, indent=2)}")
                return {
                    "type_url": any_val.type_url,
                    "value": parsed_json
                }
            else:
                print(f"any_val 不是有效JSON: '{parsed_json}'")
                return {
                    "type_url": any_val.type_url,
                    "value": parsed_json
                }
        except Exception as e:
            print(f"any_val 处理异常: {e}")
            return {
                "type_url": any_val.type_url,
                "value": decoded_info
            }

    elif safe_has_field(typed_value, "proto_bytes"):
        print(f"处理 proto_bytes 字段...")
        raw_data = typed_value.proto_bytes
        decoded_info = bytes_to_readable_string(raw_data)
        print(f"proto_bytes 原始数据: {decoded_info}")

        try:
            decoded_str = safe_decode_bytes(raw_data)
            parsed_json = try_parse_json(decoded_str)
            if isinstance(parsed_json, (dict, list)):
                print(f"proto_bytes 成功解析为JSON: {type(parsed_json)}")
                print(f"proto_bytes JSON内容: {json.dumps(parsed_json, ensure_ascii=False, indent=2)}")
                return parsed_json
            else:
                print(f"proto_bytes 不是有效JSON，返回解码字符串: '{parsed_json}'")
                return parsed_json
        except Exception as e:
            print(f"proto_bytes 处理异常: {e}")
            return decoded_info
    else:
        print(f"❌ 未识别的 TypedValue 类型，尝试完整字典转换...")

        # 尝试将整个 TypedValue 对象转换为字典
        try:
            typed_value_dict = json_format.MessageToDict(typed_value, preserving_proto_field_name=True)
            print(f"TypedValue 完整字典表示: {json.dumps(typed_value_dict, indent=2, ensure_ascii=False)}")

            # 尝试从字典中提取实际值
            for key, value in typed_value_dict.items():
                if key.endswith('_val') or key.endswith('_value'):
                    print(f"从字典中找到可能的值字段: {key} = {value}")
                    return value

            return typed_value_dict
        except Exception as e:
            print(f"TypedValue 字典转换失败: {e}")

        # 最后的备用方案
        typed_value_str = str(typed_value)
        print(f"TypedValue 字符串表示: {typed_value_str}")

        return {
            "error": "无法识别的TypedValue类型",
            "available_fields": available_fields,
            "string_representation": typed_value_str
        }

def path_to_string(path):
    """
    将gNMI Path对象转换为字符串表示，增强调试信息。
    """
    print(f"解析路径: origin='{path.origin}', elem_count={len(path.elem)}")

    path_elements = []
    if path.origin:
        path_elements.append(path.origin + ":")

    for i, elem in enumerate(path.elem):
        name = elem.name
        print(f"  路径元素 {i}: name='{name}', key_count={len(elem.key)}")

        # 不拼接 [key=val] 部分
        # if elem.key:
        #     key_parts = []
        #     for k, v in elem.key.items():
        #         key_parts.append(f"{k}={v}")
        #         print(f"    键值对: {k}={v}")
        #     name += "[" + ",".join(key_parts) + "]"
        path_elements.append(name)

    full_path = "/" + "/".join(path_elements)
    print(f"完整路径(去除key): {full_path}")
    return full_path

def explore_protobuf_structure(obj, obj_name="Unknown", max_depth=3, current_depth=0):
    """
    完整探索protobuf对象的结构
    """
    indent = "  " * current_depth
    print(f"{indent}🔍 探索 {obj_name} (类型: {type(obj).__name__})")

    if current_depth >= max_depth:
        print(f"{indent}  (达到最大深度，停止探索)")
        return

    # 获取所有字段描述符
    if hasattr(obj, 'DESCRIPTOR'):
        print(f"{indent}📋 字段描述符信息:")
        for field_desc in obj.DESCRIPTOR.fields:
            field_name = field_desc.name
            field_type = field_desc.type
            label = field_desc.label

            print(f"{indent}  - {field_name}: 类型={field_type}, 标签={label}")

            # 尝试获取字段值
            try:
                if label == field_desc.LABEL_REPEATED:
                    field_value = getattr(obj, field_name)
                    has_content = bool(field_value)
                    print(f"{indent}    重复字段，有内容: {has_content}")
                    if has_content and len(field_value) > 0:
                        print(f"{indent}    内容数量: {len(field_value)}")
                        if current_depth < max_depth - 1:
                            for i, item in enumerate(field_value[:2]):  # 只显示前2个
                                explore_protobuf_structure(item, f"{field_name}[{i}]", max_depth, current_depth + 1)
                else:
                    try:
                        has_field = obj.HasField(field_name)
                        if has_field:
                            field_value = getattr(obj, field_name)
                            print(f"{indent}    ✅ 有值: {type(field_value)} = {str(field_value)[:100]}...")

                            # 如果是复杂对象，递归探索
                            if hasattr(field_value, 'DESCRIPTOR') and current_depth < max_depth - 1:
                                explore_protobuf_structure(field_value, field_name, max_depth, current_depth + 1)
                        else:
                            print(f"{indent}    ❌ 无值")
                    except ValueError:
                        # 某些字段不能用HasField检查
                        try:
                            field_value = getattr(obj, field_name)
                            if field_value:
                                print(f"{indent}    ✅ 值(直接获取): {type(field_value)} = {str(field_value)[:100]}...")
                            else:
                                print(f"{indent}    ❌ 空值")
                        except:
                            print(f"{indent}    ❓ 无法访问")
            except Exception as e:
                print(f"{indent}    ⚠️ 访问异常: {e}")
    else:
        print(f"{indent}❌ 对象没有DESCRIPTOR属性")

    # 尝试字典转换
    try:
        obj_dict = json_format.MessageToDict(obj, preserving_proto_field_name=True)
        print(f"{indent}📄 字典表示 (键数量: {len(obj_dict)}):")
        for key, value in list(obj_dict.items())[:5]:  # 只显示前5个键
            print(f"{indent}  {key}: {type(value)} = {str(value)[:100]}...")
    except Exception as e:
        print(f"{indent}❌ 字典转换失败: {e}")

def print_raw_message(message, message_type="Unknown"):
    """
    打印原始消息的完整信息
    """
    print(f"\n{'='*80}")
    print(f"📨 原始 {message_type} 消息详细分析:")
    print(f"{'='*80}")

    # 1. 基础信息
    print(f"消息类型: {type(message)}")
    print(f"消息大小: {message.ByteSize()} bytes" if hasattr(message, 'ByteSize') else "无大小信息")

    # 2. 完整结构探索
    explore_protobuf_structure(message, message_type, max_depth=4)

    # 3. 字符串表示
    print(f"{'-'*40}")
    print(f"[字符串表示]")
    str_repr = str(message)
    print(f"长度: {len(str_repr)} 字符")
    print(f"内容: {str_repr[:500]}..." if len(str_repr) > 500 else str_repr)



def try_parse_json(data_str):
    """
    尝试多种方式解析JSON字符串
    """
    print(f"🔍 尝试解析JSON数据...")
    print(f"输入数据类型: {type(data_str)}")
    print(f"输入数据: '{data_str}'")

    if not isinstance(data_str, str):
        data_str = str(data_str)

    # 方法1：直接解析
    try:
        result = json.loads(data_str)
        print(f"✅ 直接JSON解析成功: {type(result)}")
        return result
    except json.JSONDecodeError as e:
        print(f"❌ 直接JSON解析失败: {e}")

    # 方法2：清理后解析
    try:
        cleaned_str = clean_json_string(data_str)
        result = json.loads(cleaned_str)
        print(f"✅ 清理后JSON解析成功: {type(result)}")
        return result
    except json.JSONDecodeError as e:
        print(f"❌ 清理后JSON解析失败: {e}")

    # 方法3：尝试修复常见问题
    try:
        # 替换可能的问题字符
        fixed_str = data_str

        # 移除控制字符
        import re
        fixed_str = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', fixed_str)

        # 查找并提取JSON部分
        json_match = re.search(r'(\{.*\}|\[.*\])', fixed_str, re.DOTALL)
        if json_match:
            json_part = json_match.group(1)
            print(f"正则提取的JSON部分: '{json_part}'")
            result = json.loads(json_part)
            print(f"✅ 正则修复后JSON解析成功: {type(result)}")
            return result
        else:
            print(f"❌ 正则未找到JSON结构")
    except json.JSONDecodeError as e:
        print(f"❌ 正则修复后JSON解析失败: {e}")
    except Exception as e:
        print(f"❌ 正则处理异常: {e}")

def bytes_to_readable_string(data):
    """
    将字节数据转换为可读字符串，包含十六进制表示
    """
    if isinstance(data, bytes):
        try:
            # 首先尝试UTF-8解码
            decoded = data.decode('utf-8')
            return {
                "decoded_utf8": decoded,
                "hex": binascii.hexlify(data).decode('ascii'),
                "length": len(data)
            }
        except UnicodeDecodeError:
            return {
                "decoded_replace": data.decode('utf-8', errors='replace'),
                "hex": binascii.hexlify(data).decode('ascii'),
                "length": len(data),
                "decode_error": True
            }
    return str(data)

def get_json_value(typed_value):
    """
    从TypedValue中提取JSON值，增强错误处理和调试信息。
    """
    print(f"=== TypedValue 详细分析 ===")
    print(f"TypedValue 对象类型: {type(typed_value)}")

    # 安全地打印所有可用字段和其状态
    available_fields = []
    for field_descriptor in typed_value.DESCRIPTOR.fields:
        field_name = field_descriptor.name
        try:
            if field_descriptor.label == field_descriptor.LABEL_REPEATED:
                # 对于重复字段，检查是否有内容
                has_field = bool(getattr(typed_value, field_name))
            else:
                # 对于单一字段，使用HasField检查
                has_field = typed_value.HasField(field_name)

            if has_field:
                field_value = getattr(typed_value, field_name)
                available_fields.append(field_name)
                print(f"  ✓ {field_name}: {type(field_value)} = {field_value}")
            else:
                print(f"  ✗ {field_name}: (未设置)")
        except Exception as e:
            # 某些字段可能无法用HasField检查，尝试直接获取
            try:
                field_value = getattr(typed_value, field_name)
                if field_value is not None and field_value != "":
                    available_fields.append(field_name)
                    print(f"  ✓ {field_name}: {type(field_value)} = {field_value} (直接获取)")
                else:
                    print(f"  ✗ {field_name}: (空值)")
            except:
                print(f"  ? {field_name}: (无法访问: {e})")

    print(f"活跃字段: {available_fields}")

    # 定义一个安全的字段检查函数
    def safe_has_field(obj, field_name):
        try:
            return obj.HasField(field_name)
        except ValueError:
            # 字段不存在或不能用HasField检查
            return False
        except Exception:
            return False

    # 按优先级处理各种类型
    if safe_has_field(typed_value, "json_val"):
        print(f"处理 json_val 字段...")
        raw_data = typed_value.json_val
        decoded_info = bytes_to_readable_string(raw_data)
        print(f"json_val 原始数据: {decoded_info}")
        try:
            if isinstance(raw_data, bytes):
                decoded_str = safe_decode_bytes(raw_data)
                parsed_json = try_parse_json(decoded_str)
                print(f"json_val 解析结果类型: {type(parsed_json)}")
                print(f"json_val 解析结果内容: {json.dumps(parsed_json, ensure_ascii=False, indent=2) if isinstance(parsed_json, (dict, list)) else parsed_json}")
                return parsed_json
            else:
                parsed_json = try_parse_json(str(raw_data))
                print(f"json_val 解析结果: {parsed_json}")
                return parsed_json
        except json.JSONDecodeError as e:
            print(f"json_val JSON解析失败: {e}")
            return safe_decode_bytes(raw_data) if isinstance(raw_data, bytes) else str(raw_data)
        except Exception as e:
            print(f"json_val 处理异常: {e}")
            return decoded_info

    elif safe_has_field(typed_value, "json_ietf_val"):
        print(f"处理 json_ietf_val 字段...")
        raw_data = typed_value.json_ietf_val
        decoded_info = bytes_to_readable_string(raw_data)
        print(f"json_ietf_val 原始数据: {decoded_info}")
        try:
            if isinstance(raw_data, bytes):
                decoded_str = safe_decode_bytes(raw_data)
                parsed_json = try_parse_json(decoded_str)
                print(f"json_ietf_val 解析结果类型: {type(parsed_json)}")
                print(f"json_ietf_val 解析结果内容: {json.dumps(parsed_json, ensure_ascii=False, indent=2) if isinstance(parsed_json, (dict, list)) else parsed_json}")
                return parsed_json
            else:
                parsed_json = try_parse_json(str(raw_data))
                print(f"json_ietf_val 解析结果: {parsed_json}")
                return parsed_json
        except json.JSONDecodeError as e:
            print(f"json_ietf_val JSON解析失败: {e}")
            return safe_decode_bytes(raw_data) if isinstance(raw_data, bytes) else str(raw_data)
        except Exception as e:
            print(f"json_ietf_val 处理异常: {e}")
            return decoded_info

    elif safe_has_field(typed_value, "string_val"):
        print(f"处理 string_val: {typed_value.string_val}")
        return typed_value.string_val

    elif safe_has_field(typed_value, "int_val"):
        print(f"处理 int_val: {typed_value.int_val}")
        return typed_value.int_val

    elif safe_has_field(typed_value, "uint_val"):
        print(f"处理 uint_val: {typed_value.uint_val}")
        return typed_value.uint_val

    elif safe_has_field(typed_value, "bool_val"):
        print(f"处理 bool_val: {typed_value.bool_val}")
        return typed_value.bool_val

    elif safe_has_field(typed_value, "bytes_val"):
        print(f"处理 bytes_val 字段...")
        raw_data = typed_value.bytes_val
        decoded_info = bytes_to_readable_string(raw_data)
        print(f"bytes_val 原始数据: {decoded_info}")

        try:
            decoded_str = safe_decode_bytes(raw_data)
            # 尝试解析为JSON
            parsed_json = try_parse_json(decoded_str)
            if isinstance(parsed_json, (dict, list)):
                print(f"bytes_val 成功解析为JSON: {type(parsed_json)}")
                print(f"bytes_val JSON内容: {json.dumps(parsed_json, ensure_ascii=False, indent=2)}")
                return parsed_json
            else:
                print(f"bytes_val 不是有效JSON，返回解码字符串: '{parsed_json}'")
                return parsed_json
        except Exception as e:
            print(f"bytes_val 处理异常: {e}")
            return decoded_info

    elif safe_has_field(typed_value, "float_val"):
        print(f"处理 float_val: {typed_value.float_val}")
        return typed_value.float_val

    elif safe_has_field(typed_value, "double_val"):
        print(f"处理 double_val: {typed_value.double_val}")
        return typed_value.double_val

    elif safe_has_field(typed_value, "decimal_val"):
        decimal_data = {
            "digits": typed_value.decimal_val.digits,
            "precision": typed_value.decimal_val.precision
        }
        print(f"处理 decimal_val: {decimal_data}")
        return decimal_data

    elif safe_has_field(typed_value, "leaflist_val"):
        print(f"处理 leaflist_val，包含 {len(typed_value.leaflist_val.element)} 个元素")
        leaflist_data = []
        for i, elem in enumerate(typed_value.leaflist_val.element):
            print(f"  处理 leaflist 元素 {i}:")
            elem_value = get_json_value(elem)
            leaflist_data.append(elem_value)
        print(f"leaflist_val 完整结果: {leaflist_data}")
        return leaflist_data

    elif safe_has_field(typed_value, "any_val"):
        print(f"处理 any_val 字段...")
        any_val = typed_value.any_val
        print(f"any_val type_url: {any_val.type_url}")
        raw_data = any_val.value
        decoded_info = bytes_to_readable_string(raw_data)
        print(f"any_val 原始数据: {decoded_info}")

        try:
            decoded_str = safe_decode_bytes(raw_data)
            parsed_json = try_parse_json(decoded_str)
            if isinstance(parsed_json, (dict, list)):
                print(f"any_val 成功解析为JSON: {type(parsed_json)}")
                print(f"any_val JSON内容: {json.dumps(parsed_json, ensure_ascii=False, indent=2)}")
                return {
                    "type_url": any_val.type_url,
                    "value": parsed_json
                }
            else:
                print(f"any_val 不是有效JSON: '{parsed_json}'")
                return {
                    "type_url": any_val.type_url,
                    "value": parsed_json
                }
        except Exception as e:
            print(f"any_val 处理异常: {e}")
            return {
                "type_url": any_val.type_url,
                "value": decoded_info
            }

    elif safe_has_field(typed_value, "proto_bytes"):
        print(f"处理 proto_bytes 字段...")
        raw_data = typed_value.proto_bytes
        decoded_info = bytes_to_readable_string(raw_data)
        print(f"proto_bytes 原始数据: {decoded_info}")

        try:
            decoded_str = safe_decode_bytes(raw_data)
            parsed_json = try_parse_json(decoded_str)
            if isinstance(parsed_json, (dict, list)):
                print(f"proto_bytes 成功解析为JSON: {type(parsed_json)}")
                print(f"proto_bytes JSON内容: {json.dumps(parsed_json, ensure_ascii=False, indent=2)}")
                return parsed_json
            else:
                print(f"proto_bytes 不是有效JSON，返回解码字符串: '{parsed_json}'")
                return parsed_json
        except Exception as e:
            print(f"proto_bytes 处理异常: {e}")
            return decoded_info
    else:
        print(f"❌ 未识别的 TypedValue 类型，尝试完整字典转换...")

        # 尝试将整个 TypedValue 对象转换为字典
        try:
            typed_value_dict = json_format.MessageToDict(typed_value, preserving_proto_field_name=True)
            print(f"TypedValue 完整字典表示: {json.dumps(typed_value_dict, indent=2, ensure_ascii=False)}")

            # 尝试从字典中提取实际值
            for key, value in typed_value_dict.items():
                if key.endswith('_val') or key.endswith('_value'):
                    print(f"从字典中找到可能的值字段: {key} = {value}")
                    return value

            return typed_value_dict
        except Exception as e:
            print(f"TypedValue 字典转换失败: {e}")

        # 最后的备用方案
        typed_value_str = str(typed_value)
        print(f"TypedValue 字符串表示: {typed_value_str}")

        return {
            "error": "无法识别的TypedValue类型",
            "available_fields": available_fields,
            "string_representation": typed_value_str
        }

def path_to_string(path, include_keys=True, include_origin=False):
    """
    将 gNMI Path 对象转换为字符串。
    - include_keys: 是否包含 key（如 component[name=...])
    - include_origin: 是否包含 origin 前缀（如 openconfig:）
    """
    elems = []
    if include_origin and getattr(path, 'origin', None):
        # origin 作为前缀，后面仍然用斜杠拼接 elem
        elems.append(path.origin + ":")

    for elem in getattr(path, 'elem', []):
        name = elem.name
        if include_keys and getattr(elem, 'key', None):
            # 使用稳定顺序输出 key
            kv_pairs = []
            try:
                for k in sorted(elem.key.keys()):
                    kv_pairs.append(f"{k}={elem.key[k]}")
            except Exception:
                # 部分实现里 key 可能不可迭代（极少数情况），降级为不带 key
                elems.append(name)
                continue
            elems.append(f"{name}[{','.join(kv_pairs)}]")
        else:
            elems.append(name)

    # 规整输出
    s = "/" + "/".join(elems)
    # 防御性清理历史数据里可能遗留的 [key] 片段
    return s
def normalize_path_no_keys(path_str: str) -> str:
    import re
    return re.sub(r"\[.*?\]", "", path_str)


def find_sensor_root_for_path(full_path: str) -> str:
    clean = normalize_path_no_keys(full_path)
    for root in SENSOR_ROOTS:
        if clean == root or clean.startswith(root + "/"):
            return root
    return ""


def _to_float(val):
    try:
        if isinstance(val, (int, float)):
            return float(val)
        if isinstance(val, str):
            return float(val)
    except Exception:
        return None
    return None


def _to_int(val):
    try:
        if isinstance(val, int):
            return val
        if isinstance(val, float):
            return int(val)
        if isinstance(val, str):
            return int(float(val))
    except Exception:
        return None
    return None


def pick_instant_or_number(v):
    if isinstance(v, dict):
        for k in ("instant", "value", "avg", "min", "max"):
            if k in v:
                f = _to_float(v[k])
                if f is not None:
                    return f
        return None
    return _to_float(v)


def extract_fields_for_root(root: str, val) -> dict:
    # 仅处理 dict/list/标量中的常见字段；其余保持空
    out = {}
    if isinstance(val, dict):
        if root.endswith("ethernet/state"):
            # counters 期望
            if "counters" in val and isinstance(val["counters"], dict):
                counters = val["counters"]
                sub = {}
                for k in ("in-octets", "out-octets", "in-pkts", "out-pkts"):
                    if k in counters:
                        iv = _to_int(counters[k])
                        if iv is not None:
                            sub[k] = iv
                if sub:
                    out["counters"] = sub
        if "output-power" in val:
            f = pick_instant_or_number(val["output-power"]) ;
            if f is not None: out["output-power"] = f
        if "input-power" in val:
            f = pick_instant_or_number(val["input-power"]) ;
            if f is not None: out["input-power"] = f
        if "laser-bias-current" in val:
            f = pick_instant_or_number(val["laser-bias-current"]) ;
            if f is not None: out["laser-bias-current"] = f
        if "temperature" in val:
            temp = val["temperature"]
            if isinstance(temp, dict):
                f = pick_instant_or_number(temp)
                if f is not None:
                    out["temperature"] = f
            else:
                f = _to_float(temp)
                if f is not None:
                    out["temperature"] = f
        if "memory" in val and isinstance(val["memory"], dict):
            mem = {}
            for k in ("utilized", "available"):
                if k in val["memory"]:
                    iv = _to_int(val["memory"][k])
                    if iv is not None:
                        mem[k] = iv
            if mem:
                out["memory"] = mem
        for k in ("frequency", "target-output-power", "operational-mode"):
            if k in val:
                f = _to_float(val[k])
                if f is not None:
                    # operational-mode 可能是整数
                    out[k] = int(f) if k == "operational-mode" else f
    elif isinstance(val, (int, float, str)):
        # 标量值：难以定位字段名，交由上层合并时决定是否保留
        pass
    return out


def merge_sensor_path_data(target: dict, new_data: dict):
    # 深度合并（仅一层），用于合并 counters/memory 等子对象
    for k, v in new_data.items():
        if isinstance(v, dict) and isinstance(target.get(k), dict):
            target[k].update(v)
        else:
            target[k] = v



def combine_path_to_string(prefix_path, rel_path, include_keys=True, include_origin=False):
    """
    将 Notification.prefix 与 Update.path 合并为一个完整字符串。
    - 默认包含 key，以便区分不同 component/name 的消息
    - origin 仅对 prefix 可选保留
    """
    parts = []
    if prefix_path is not None:
        p = path_to_string(prefix_path, include_keys=include_keys, include_origin=include_origin)
        parts.append(p.strip("/"))
    if rel_path is not None:
        c = path_to_string(rel_path, include_keys=include_keys, include_origin=False)
        parts.append(c.strip("/"))
    combined = "/" + "/".join([p for p in parts if p])
    # 清理可能出现的重复斜杠
    while '//' in combined:
        combined = combined.replace('//', '/')
    return combined


def shorten_path_string(path_str, keep_last=3):
    """保留路径最后 N 段，便于聚合展示。"""
    segs = [s for s in path_str.split('/') if s]
    if len(segs) <= keep_last:
        return "/" + "/".join(segs)
    return "/" + "/".join(segs[-keep_last:])



class TelemetryServicer(gnmi_dialout_pb2_grpc.gNMIDialoutServicer):
    """
    gNMI Dialout Telemetry Servicer实现。
    """
    def Publish(self, request_iterator, context):
        peer_address = context.peer()
        # 从 peer_address 中提取 IP 地址
        if peer_address.startswith("ipv4:"):
            device_ip = peer_address.split(":")[1]
        elif peer_address.startswith("ipv6:"):
            device_ip = peer_address.split(":", 2)[1] if peer_address.count(":") >= 2 else peer_address.split(":")[1]
        else:
            device_ip = peer_address.split(":")[0]

        print(f"\n🔗 收到来自设备 {device_ip} 的连接 (peer: {peer_address})")

        # 为每个设备和日期创建单独的文件
        current_date = datetime.now().strftime("%Y%m%d")
        file_name = f"{device_ip}_{current_date}.json"
        file_path = os.path.join(DATA_DIR, file_name)

        # 文件管理
        if device_ip not in device_data_files or device_data_files[device_ip].name != file_path:
            if device_ip in device_data_files:
                device_data_files[device_ip].close()
                print(f"📁 关闭设备 {device_ip} 的旧文件")
            device_data_files[device_ip] = open(file_path, 'a', encoding='utf-8')
            device_data_locks[device_ip] = threading.Lock()
            print(f"📁 为设备 {device_ip} 创建数据文件: {file_path}")

        message_count = 0
        try:
            for request in request_iterator:
                message_count += 1
                print(f"\n📥 设备 {device_ip} - 消息 #{message_count}")
                # 新增：打印原始消息的字符串内容，便于分析
                try:
                    str_repr = str(request)
                    #print(f"[原始消息字符串] 长度: {len(str_repr)} 字符\n内容: {str_repr[:1000]}{'...' if len(str_repr) > 1000 else ''}")
                    print(f"[原始消息字符串] 长度: {len(str_repr)} 字符\n内容: {str_repr}")
                except Exception as e:
                    print(f"[原始消息字符串] 获取失败: {e}")

                # # 打印完整的原始消息
                # print_raw_message(request, f"gNMI Dialout Request #{message_count}")


                # 确定消息类型
                oneof_field = request.WhichOneof('response')
                print(f"🏷️  消息类型: {oneof_field}")
                if oneof_field == "update":
                    #self._handle_update(request.update, device_ip, message_count)
                    self._handle_update(request, device_ip, message_count)
                elif oneof_field == "sync_response":
                    self._handle_sync_response(request.sync_response, device_ip, message_count)
                elif request.HasField("error"):
                    self._handle_error(request.error, device_ip, message_count)
                else:
                    print(f"⚠️  设备 {device_ip} - 消息 #{message_count}: 未知或空消息类型")
                    # 仍然保存未知消息
                    unknown_data = {
                        "message_number": message_count,
                        "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f'),
                        "device_ip": device_ip,
                        "message_type": "unknown",
                        "oneof_field": oneof_field,
                        "raw_message": self._safe_message_to_dict(request)
                    }
                    self._save_to_file(device_ip, unknown_data)
        except grpc.RpcError as e:
            print(f"❌ 设备 {device_ip} gRPC流错误: {e.code()} - {e.details()}")
        except Exception as e:
            print(f"❌ 设备 {device_ip} 未知错误: {e}")
            import traceback
            traceback.print_exc()
        finally:
            print(f"📤 设备 {device_ip} 连接结束，共处理 {message_count} 条消息")
            return gnmi_base_pb2.PublishResponse()

    def _handle_update(self, notification, device_ip, message_count):
        """
        处理更新消息：
        - 组合 prefix + path，包含 origin 与 key，避免不同组件被折叠
        - 解析 Update.value (Value: bytes + Encoding) 为结构化数据
        - 打印并保存更可读的数据
        """
        print(f"📊 处理更新消息...")

        # 1) 提取前缀与更新列表/删除列表
        prefix = None
        updates = []
        deletes = []
        try:
            if hasattr(notification, 'update') and notification.update is not None:
                # notification 为 SubscribeResponse，notification.update 为 Notification
                notif = notification.update
                if hasattr(notif, 'prefix'):
                    prefix = notif.prefix
                if hasattr(notif, 'update'):
                    updates = list(notif.update)
                if hasattr(notif, 'delete'):
                    deletes = list(notif.delete)
        except Exception as e:
            print(f"⚠️ 提取 prefix/updates/deletes 失败: {e}")

        # 2) 汇总并打印 path（包含origin与keys）
        path_set = set()
        for up in updates:
            try:
                full_p = combine_path_to_string(prefix, up.path, include_keys=True, include_origin=True)
                path_set.add(full_p)
            except Exception as e:
                print(f"⚠️ 解析 update.path 失败: {e}")
        for dp in deletes:
            try:
                full_p = combine_path_to_string(prefix, dp, include_keys=True, include_origin=True)
                path_set.add(full_p + " (deleted)")
            except Exception as e:
                print(f"⚠️ 解析 delete.path 失败: {e}")
        if path_set:
            print(f"🧩 本次消息包含路径共 {len(path_set)} 个:")
            for p in sorted(path_set):
                print(f"  - {p}")

        # 3) 构造结构化的 parsed_updates，解析每条 update 的值
        parsed_updates = []
        for idx, up in enumerate(updates):
            try:
                full_path = combine_path_to_string(prefix, up.path, include_keys=True, include_origin=True)
                short_path = shorten_path_string(full_path, keep_last=4)
                val = None
                if hasattr(up, 'value') and up.value is not None:
                    val = process_gnmi_value(up.value)
                parsed_updates.append({
                    "message_number": message_count,
                    "index": idx,
                    "device_ip": device_ip,
                    "path_full": full_path,
                    "path_short": short_path,
                    "duplicates": getattr(up, 'duplicates', 0),
                    "value": val,
                })
            except Exception as e:
                print(f"⚠️ 解析 update[{idx}] 失败: {e}")

        # 4) 聚合为 telemetry_monitor_new 的目标结构并保存一条记录
        try:
            # 目标结构：
            # {
            #   "timestamp": ISO8601,
            #   "device_id": device_ip,
            #   "subscription": SUBSCRIPTION_NAME,
            #   "sensor_paths": { root_path: {...}, ... }
            # }
            sensor_paths = {}
            for item in parsed_updates:
                full_path = item.get("path_full", "")
                root = find_sensor_root_for_path(full_path)
                if not root:
                    continue
                val = item.get("value")
                fields = extract_fields_for_root(root, val)
                if not fields:
                    # 有些设备直接把数值放在叶子，或 JSON 在混合文本中被提取出来但没有标准键
                    # 这里先跳过，避免写入不期望的结构
                    continue
                if root not in sensor_paths:
                    sensor_paths[root] = {}
                merge_sensor_path_data(sensor_paths[root], fields)

            # 时间戳：从 Notification.update.timestamp（ns）→ ISO8601；若不可用则用现在
            iso_ts = datetime.now().isoformat()
            try:
                notif = notification.update if hasattr(notification, 'update') else None
                if notif and hasattr(notif, 'timestamp'):
                    ts_int = int(getattr(notif, 'timestamp'))
                    dt = datetime.fromtimestamp(ts_int / 1_000_000_000)
                    iso_ts = dt.isoformat()
            except Exception:
                pass

            out_record = {
                "timestamp": iso_ts,
                "device_id": device_ip,
                "subscription": SUBSCRIPTION_NAME,
                "sensor_paths": sensor_paths
            }

            # 仅当有有效字段时写入，避免空行
            if sensor_paths:
                self._save_to_file(device_ip, out_record)
            else:
                print("ℹ️ 本条 Notification 未提取到匹配的 sensor_paths 字段，跳过写入目标结构")
        except Exception as e:
            print(f"[保存notification] 构造目标结构失败: {e}")
        print(f"✅ 更新消息处理完成: #{message_count}, 解析 {len(parsed_updates)} 条更新")

    def _handle_sync_response(self, sync_response, device_ip, message_count):
        """处理同步响应消息"""
        print(f"🔄 处理同步响应消息...")
        print_raw_message(sync_response, f"Sync Response #{message_count}")

        sync_data = {
            "message_number": message_count,
            "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f'),
            "device_ip": device_ip,
            "message_type": "sync_response",
            "raw_sync_response": self._safe_message_to_dict(sync_response)
        }

        self._save_to_file(device_ip, sync_data)
        print(f"✅ 同步响应处理完成")

    def _handle_error(self, error, device_ip, message_count):
        """处理错误消息"""
        print(f"❌ 处理错误消息...")
        print_raw_message(error, f"Error #{message_count}")

        error_data = {
            "message_number": message_count,
            "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f'),
            "device_ip": device_ip,
            "message_type": "error",
            "error_code": error.code,
            "error_message": error.message,
            "raw_error": self._safe_message_to_dict(error)
        }

        self._save_to_file(device_ip, error_data)
        print(f"❌ 错误消息处理完成: {error.message}")

    def _safe_message_to_dict(self, message):
        """安全地将protobuf消息转换为字典"""
        try:
            return json_format.MessageToDict(message, preserving_proto_field_name=True)
        except Exception as e:
            return {"conversion_error": f"无法转换为字典: {e}", "string_repr": str(message)}

    def _save_to_file(self, device_ip, data):
        """保存数据到文件"""
        try:
            with device_data_locks[device_ip]:
                json_str = json.dumps(data, ensure_ascii=False, indent=None)
                device_data_files[device_ip].write(json_str + "\n")
                device_data_files[device_ip].flush()
            print(f"💾 数据已保存到文件 (设备: {device_ip})")
        except Exception as e:
            print(f"❌ 保存文件失败 (设备: {device_ip}): {e}")

def serve():
    """启动gRPC服务器"""
    # 启用详细日志
    os.environ['GRPC_VERBOSITY'] = 'INFO'

    server_address = '0.0.0.0:50051'
    server = grpc.server(concurrent.futures.ThreadPoolExecutor(max_workers=20))
    gnmi_dialout_pb2_grpc.add_gNMIDialoutServicer_to_server(TelemetryServicer(), server)
    server.add_insecure_port(server_address)
    server.start()

    print(f"🚀 gNMI Dialout服务器启动成功!")
    print(f"📡 监听地址: {server_address}")
    print(f"📂 数据目录: {DATA_DIR}")
    print(f"⏰ 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"{'='*60}")
    print(f"等待设备连接...")

    try:
        while True:
            time.sleep(86400)  # 每天检查一次
    except KeyboardInterrupt:
        print(f"\n🛑 收到中断信号，服务器关闭中...")
        server.stop(grace=5)

        # 关闭所有文件
        for device_ip, file_handle in device_data_files.items():
            try:
                file_handle.close()
                print(f"📁 已关闭设备 {device_ip} 的数据文件")
            except Exception as e:
                print(f"❌ 关闭设备 {device_ip} 文件失败: {e}")

        print(f"✅ 服务器已安全关闭")

if __name__ == '__main__':
    serve()