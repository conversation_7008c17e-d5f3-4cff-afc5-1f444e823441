#!/usr/bin/env python3
"""
多设备 Telemetry 监控器
同时监控多个设备的 telemetry 数据
"""

import socket
import json
import time
import threading
from datetime import datetime
from collections import defaultdict, deque

class MultiDeviceTelemetryMonitor:
    """多设备 Telemetry 监控器"""
    
    def __init__(self, host='0.0.0.0', port=57400):
        self.host = host
        self.port = port
        self.running = False
        self.server_socket = None
        self.clients = []
        self.total_data_count = 0
        self.start_time = None
        
        # 按设备分类的统计信息
        self.device_stats = defaultdict(lambda: {
            'message_count': 0,
            'last_seen': None,
            'transceivers': {},
            'optical_channels': {},
            'logical_channels': {'otn': {}, 'ethernet': {}},
            'cpu_utilization': {},
            'component_states': {},
            'recent_data': deque(maxlen=50)  # 每个设备保留最近50条数据
        })
        
        # 全局统计
        self.global_stats = {
            'total_devices': 0,
            'active_devices': set(),
            'message_rates': deque(maxlen=60),  # 最近60秒的消息速率
            'device_message_rates': defaultdict(lambda: deque(maxlen=60))
        }
    
    def start(self):
        """启动监控器"""
        
        print("🌟 多设备 Telemetry 监控器")
        print(f"📡 监听地址: {self.host}:{self.port}")
        print(f"📍 收集器 IP: ***************")
        print(f"⏰ 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("🔍 等待多设备连接...")
        print("=" * 80)
        
        try:
            # 创建服务器套接字
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.server_socket.bind((self.host, self.port))
            self.server_socket.listen(20)  # 增加监听队列以支持更多设备
            
            self.running = True
            self.start_time = datetime.now()
            
            # 启动各种后台线程
            self._start_background_threads()
            
            while self.running:
                try:
                    # 接受连接
                    client_socket, client_address = self.server_socket.accept()
                    print(f"\n🔗 新连接: {client_address}")
                    print(f"⏰ 连接时间: {datetime.now().strftime('%H:%M:%S')}")
                    
                    self.clients.append(client_socket)
                    
                    # 为每个客户端启动处理线程
                    client_thread = threading.Thread(
                        target=self._handle_client,
                        args=(client_socket, client_address)
                    )
                    client_thread.daemon = True
                    client_thread.start()
                    
                except Exception as e:
                    if self.running:
                        print(f"❌ 接受连接失败: {e}")
                    
        except Exception as e:
            print(f"❌ 启动监控器失败: {e}")
        
        finally:
            try:
                self.server_socket.close()
            except:
                pass
    
    def _start_background_threads(self):
        """启动后台线程"""
        
        # 状态报告线程
        status_thread = threading.Thread(target=self._status_reporter)
        status_thread.daemon = True
        status_thread.start()
        
        # 设备活跃度监控线程
        activity_thread = threading.Thread(target=self._device_activity_monitor)
        activity_thread.daemon = True
        activity_thread.start()
        
        # 数据分析线程
        analysis_thread = threading.Thread(target=self._data_analyzer)
        analysis_thread.daemon = True
        analysis_thread.start()
    
    def _handle_client(self, client_socket, client_address):
        """处理客户端数据"""
        
        buffer = ""
        
        try:
            while self.running:
                data = client_socket.recv(8192)
                if not data:
                    break
                
                try:
                    decoded_data = data.decode('utf-8')
                    buffer += decoded_data
                    
                    # 处理完整的消息
                    while '\n' in buffer:
                        line, buffer = buffer.split('\n', 1)
                        if line.strip():
                            self._process_message(line.strip(), client_address)
                
                except UnicodeDecodeError:
                    # 处理二进制数据
                    self._process_binary_message(data, client_address)
        
        except Exception as e:
            print(f"❌ 处理客户端 {client_address} 数据失败: {e}")
        
        finally:
            try:
                client_socket.close()
                if client_socket in self.clients:
                    self.clients.remove(client_socket)
                print(f"🔌 客户端 {client_address} 断开连接")
            except:
                pass
    
    def _process_message(self, data, client_address):
        """处理文本消息"""
        
        self.total_data_count += 1
        timestamp = datetime.now()
        
        # 尝试解析 JSON
        try:
            json_data = json.loads(data)
            device_name = json_data.get('device', f'unknown-{client_address[0]}')
            
            # 更新设备统计
            self._update_device_stats(device_name, json_data, timestamp, client_address)
            
            # 显示消息
            self._display_device_message(device_name, json_data, timestamp, client_address)
            
        except json.JSONDecodeError:
            device_name = f'unknown-{client_address[0]}'
            print(f"\n📝 [{timestamp.strftime('%H:%M:%S')}] 原始数据来自 {device_name}")
            print(f"   数据: {data[:100]}...")
        
        # 保存数据
        self._save_data(data, timestamp, client_address)
    
    def _update_device_stats(self, device_name, data, timestamp, client_address):
        """更新设备统计信息"""
        
        stats = self.device_stats[device_name]
        stats['message_count'] += 1
        stats['last_seen'] = timestamp
        stats['recent_data'].append({
            'timestamp': timestamp,
            'data': data,
            'client_address': client_address
        })
        
        # 更新全局统计
        self.global_stats['active_devices'].add(device_name)
        self.global_stats['total_devices'] = len(self.global_stats['active_devices'])
        
        # 提取光传输相关数据
        optical_data = self._extract_optical_data(data)
        
        # 更新设备特定统计
        for category, items in optical_data.items():
            if category in stats and isinstance(items, dict):
                for item_id, item_data in items.items():
                    if item_id not in stats[category]:
                        stats[category][item_id] = []
                    
                    stats[category][item_id].append({
                        'timestamp': timestamp,
                        'data': item_data
                    })
                    
                    # 只保留最近50个数据点
                    if len(stats[category][item_id]) > 50:
                        stats[category][item_id] = stats[category][item_id][-50:]
    
    def _display_device_message(self, device_name, data, timestamp, client_address):
        """显示设备消息"""
        
        device_count = self.device_stats[device_name]['message_count']
        timestamp_str = timestamp.strftime('%H:%M:%S.%f')[:-3]
        
        print(f"\n📊 [{timestamp_str}] 设备: {device_name} | 消息 #{device_count} | 来自: {client_address}")
        print("=" * 80)
        
        # 显示基本信息
        if 'sensor_group' in data:
            print(f"📡 传感器组: {data['sensor_group']}")
        
        if 'subscription' in data:
            print(f"📋 订阅: {data['subscription']}")
        
        if 'sequence' in data:
            print(f"🔢 序列号: {data['sequence']}")
        
        # 提取和显示光传输数据
        optical_data = self._extract_optical_data(data)
        
        if optical_data['transceivers']:
            print("\n🔌 光收发器状态:")
            for transceiver_id, info in list(optical_data['transceivers'].items())[:2]:  # 只显示前2个
                print(f"   {transceiver_id}: {self._format_optical_info(info)}")
        
        if optical_data['cpu_utilization']:
            print("\n💻 CPU 利用率:")
            for cpu_id, utilization in optical_data['cpu_utilization'].items():
                if isinstance(utilization, dict) and 'instant' in utilization:
                    print(f"   {cpu_id}: {utilization['instant']}%")
        
        if optical_data['logical_channels']['otn'] or optical_data['logical_channels']['ethernet']:
            print("\n📊 逻辑通道:")
            otn_count = len(optical_data['logical_channels']['otn'])
            eth_count = len(optical_data['logical_channels']['ethernet'])
            print(f"   OTN: {otn_count} 个, 以太网: {eth_count} 个")
        
        print("-" * 60)
    
    def _extract_optical_data(self, data):
        """提取光传输相关数据"""
        
        optical_data = {
            'transceivers': {},
            'optical_channels': {},
            'logical_channels': {'otn': {}, 'ethernet': {}},
            'cpu_utilization': {},
            'component_states': {},
            'physical_channels': {}
        }
        
        def search_optical_data(obj, path=""):
            if isinstance(obj, dict):
                for key, value in obj.items():
                    current_path = f"{path}/{key}" if path else key
                    
                    if 'transceiver' in key.lower():
                        optical_data['transceivers'][current_path] = value
                    elif 'optical-channel' in key.lower():
                        optical_data['optical_channels'][current_path] = value
                    elif 'otn' in key.lower():
                        optical_data['logical_channels']['otn'][current_path] = value
                    elif 'ethernet' in key.lower():
                        optical_data['logical_channels']['ethernet'][current_path] = value
                    elif 'cpu' in key.lower() and 'utilization' in key.lower():
                        optical_data['cpu_utilization'][current_path] = value
                    elif 'component' in key.lower() and 'state' in key.lower():
                        optical_data['component_states'][current_path] = value
                    elif 'physical-channel' in key.lower():
                        optical_data['physical_channels'][current_path] = value
                    
                    if isinstance(value, (dict, list)):
                        search_optical_data(value, current_path)
            
            elif isinstance(obj, list):
                for i, item in enumerate(obj):
                    search_optical_data(item, f"{path}[{i}]")
        
        search_optical_data(data)
        return optical_data
    
    def _format_optical_info(self, info):
        """格式化光学信息显示"""
        if isinstance(info, dict):
            key_values = []
            for key, value in list(info.items())[:3]:  # 只显示前3个字段
                if isinstance(value, (int, float)):
                    if 'power' in key.lower():
                        key_values.append(f"{key}={value:.2f}dBm")
                    elif 'temperature' in key.lower():
                        key_values.append(f"{key}={value:.1f}°C")
                    else:
                        key_values.append(f"{key}={value}")
                else:
                    key_values.append(f"{key}={str(value)[:20]}")
            return ", ".join(key_values)
        return str(info)[:50]
    
    def _process_binary_message(self, data, client_address):
        """处理二进制消息"""
        
        self.total_data_count += 1
        timestamp = datetime.now()
        
        print(f"\n📊 [{timestamp.strftime('%H:%M:%S')}] 二进制数据来自 {client_address}")
        print(f"📏 数据长度: {len(data)} 字节")
    
    def _save_data(self, data, timestamp, client_address):
        """保存数据到文件"""
        try:
            filename = f"multi_device_telemetry_{datetime.now().strftime('%Y%m%d')}.log"
            with open(filename, 'a', encoding='utf-8') as f:
                f.write(f"[{timestamp.strftime('%H:%M:%S.%f')[:-3]}] {client_address}: {data}\n")
        except Exception as e:
            print(f"❌ 保存数据失败: {e}")
    
    def _status_reporter(self):
        """状态报告线程"""
        while self.running:
            time.sleep(30)  # 每30秒报告一次
            if self.running and self.start_time:
                self._print_status_report()
    
    def _print_status_report(self):
        """打印状态报告"""
        runtime = datetime.now() - self.start_time
        
        print(f"\n📊 === 多设备监控状态报告 ===")
        print(f"⏱️  运行时间: {runtime}")
        print(f"📨 总消息数: {self.total_data_count}")
        print(f"🔗 活跃连接: {len(self.clients)}")
        print(f"📱 活跃设备: {len(self.global_stats['active_devices'])}")
        
        # 按设备显示统计
        print(f"\n📋 设备详情:")
        for device_name in sorted(self.global_stats['active_devices']):
            stats = self.device_stats[device_name]
            last_seen = stats['last_seen']
            time_since = datetime.now() - last_seen if last_seen else None
            
            if time_since and time_since.total_seconds() < 60:
                status = "🟢 活跃"
            elif time_since and time_since.total_seconds() < 300:
                status = "🟡 空闲"
            else:
                status = "🔴 离线"
            
            print(f"   {device_name}: {status} | 消息: {stats['message_count']} | 最后: {last_seen.strftime('%H:%M:%S') if last_seen else 'N/A'}")
        
        print("=" * 50)
    
    def _device_activity_monitor(self):
        """设备活跃度监控线程"""
        while self.running:
            time.sleep(60)  # 每分钟检查一次
            if self.running:
                self._check_device_activity()
    
    def _check_device_activity(self):
        """检查设备活跃度"""
        current_time = datetime.now()
        inactive_devices = []
        
        for device_name, stats in self.device_stats.items():
            if stats['last_seen']:
                time_since = current_time - stats['last_seen']
                if time_since.total_seconds() > 300:  # 5分钟无数据
                    inactive_devices.append(device_name)
        
        if inactive_devices:
            print(f"\n⚠️  检测到 {len(inactive_devices)} 个设备超过5分钟无数据:")
            for device in inactive_devices:
                print(f"   📱 {device}")
    
    def _data_analyzer(self):
        """数据分析线程"""
        while self.running:
            time.sleep(120)  # 每2分钟分析一次
            if self.running:
                self._analyze_device_trends()
    
    def _analyze_device_trends(self):
        """分析设备趋势"""
        if not self.device_stats:
            return
        
        print(f"\n🔍 === 设备趋势分析 ===")
        
        for device_name, stats in self.device_stats.items():
            if stats['message_count'] >= 5:  # 至少有5条消息才分析
                print(f"📱 {device_name}:")
                
                # 分析消息频率
                recent_data = list(stats['recent_data'])
                if len(recent_data) >= 2:
                    time_diff = recent_data[-1]['timestamp'] - recent_data[0]['timestamp']
                    if time_diff.total_seconds() > 0:
                        rate = len(recent_data) / time_diff.total_seconds() * 60  # 每分钟消息数
                        print(f"   📊 消息频率: {rate:.1f} 消息/分钟")
                
                # 分析 CPU 利用率趋势
                if stats['cpu_utilization']:
                    for cpu_id, cpu_history in stats['cpu_utilization'].items():
                        if len(cpu_history) >= 3:
                            recent_values = [item['data'].get('instant', 0) for item in cpu_history[-3:] if isinstance(item['data'], dict)]
                            if recent_values:
                                avg_cpu = sum(recent_values) / len(recent_values)
                                print(f"   💻 {cpu_id}: 平均 {avg_cpu:.1f}%")
        
        print("-" * 40)
    
    def stop(self):
        """停止监控器"""
        self.running = False
        
        for client in self.clients:
            try:
                client.close()
            except:
                pass
        self.clients.clear()
        
        if self.server_socket:
            try:
                self.server_socket.close()
            except:
                pass
        
        print("🛑 多设备监控器已停止")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='多设备 Telemetry 监控器')
    parser.add_argument('--host', default='0.0.0.0', help='监听地址')
    parser.add_argument('--port', type=int, default=57400, help='监听端口')
    
    args = parser.parse_args()
    
    monitor = MultiDeviceTelemetryMonitor(args.host, args.port)
    
    try:
        monitor.start()
    except KeyboardInterrupt:
        print("\n收到停止信号...")
        monitor.stop()
    except Exception as e:
        print(f"监控器运行失败: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    import sys
    sys.exit(main())
