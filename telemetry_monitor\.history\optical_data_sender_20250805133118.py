#!/usr/bin/env python3
"""
光传输设备数据发送器
持续发送模拟的光传输设备 telemetry 数据
"""

import socket
import json
import time
from datetime import datetime

def send_optical_data(device_name="nokia-alu-1"):
    """持续发送光传输设备数据"""

    collector_ip = "***************"
    collector_port = 57400

    print(f"🌟 启动光传输设备数据发送器")
    print(f"📱 设备名称: {device_name}")
    print(f"📡 目标收集器: {collector_ip}:{collector_port}")
    print(f"⏰ 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🔄 开始发送数据...")
    print("=" * 80)

    counter = 0
    
    while True:
        try:
            counter += 1
            
            # 连接到收集器
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            sock.connect((collector_ip, collector_port))
            
            # 生成光传输设备数据
            message = {
                "timestamp": int(time.time() * 1000),
                "device": device_name,
                "sensor_group": "allSg",
                "subscription": "optical-monitoring",
                "sequence": counter,
                "data": {
                    "openconfig-platform:components": {
                        "component": [
                            {
                                "name": f"transceiver-1/1/{(counter % 4) + 1}",
                                "openconfig-platform-transceiver:transceiver": {
                                    "state": {
                                        "present": "PRESENT",
                                        "enabled": True,
                                        "fault-condition": False,
                                        "form-factor": "QSFP28",
                                        "vendor": "Nokia",
                                        "vendor-part": "3HE12345AA",
                                        "serial-no": f"ABC{123456789 + counter}",
                                        "connector-type": "LC"
                                    },
                                    "physical-channels": {
                                        "channel": [
                                            {
                                                "index": 0,
                                                "state": {
                                                    "index": 0,
                                                    "description": "Lane 0",
                                                    "tx-laser": True,
                                                    "output-power": round(-2.0 + (counter % 10) * 0.1, 2),
                                                    "input-power": round(-3.0 + (counter % 8) * 0.1, 2),
                                                    "laser-bias-current": round(40.0 + (counter % 20), 1),
                                                    "target-output-power": -2.0
                                                }
                                            }
                                        ]
                                    }
                                }
                            },
                            {
                                "name": "optical-channel-1/1/1/1",
                                "openconfig-terminal-device:optical-channel": {
                                    "state": {
                                        "frequency": 196100000 + (counter % 100) * 1000,
                                        "target-output-power": 0.0,
                                        "operational-mode": 1,
                                        "line-port": "port-1/1/1"
                                    }
                                }
                            },
                            {
                                "name": "cpu-0",
                                "cpu": {
                                    "openconfig-platform-cpu:utilization": {
                                        "state": {
                                            "instant": round(20.0 + (counter % 30), 1),
                                            "avg": round(25.0 + (counter % 15), 1),
                                            "min": 15.0,
                                            "max": 45.0,
                                            "interval": 300000000
                                        }
                                    }
                                }
                            }
                        ]
                    },
                    "openconfig-terminal-device:terminal-device": {
                        "logical-channels": {
                            "channel": [
                                {
                                    "index": 1,
                                    "otn": {
                                        "state": {
                                            "tributary-slot-granularity": "TRIB_SLOT_1_25G",
                                            "pre-fec-ber": {
                                                "instant": round((1.0 + (counter % 5) * 0.1) * 1e-12, 15),
                                                "avg": 1.1e-12,
                                                "min": 9.8e-13,
                                                "max": 1.5e-12
                                            },
                                            "post-fec-ber": {
                                                "instant": 0.0,
                                                "avg": 0.0,
                                                "min": 0.0,
                                                "max": 0.0
                                            },
                                            "q-value": {
                                                "instant": round(15.0 + (counter % 10) * 0.1, 1),
                                                "avg": 15.1,
                                                "min": 14.8,
                                                "max": 15.5
                                            }
                                        }
                                    },
                                    "ethernet": {
                                        "state": {
                                            "in-mac-control-frames": counter % 100,
                                            "in-mac-pause-frames": 0,
                                            "in-oversize-frames": counter % 10,
                                            "in-undersize-frames": 0,
                                            "in-fragment-frames": 0,
                                            "in-8021q-frames": 1000000 + counter * 1000,
                                            "out-mac-control-frames": counter % 95,
                                            "out-mac-pause-frames": 0,
                                            "out-8021q-frames": 1000000 + counter * 950
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
            
            # 发送数据
            json_data = json.dumps(message) + "\n"
            sock.send(json_data.encode('utf-8'))
            sock.close()
            
            # 显示发送状态
            timestamp = datetime.now().strftime('%H:%M:%S')
            transceiver_name = f"transceiver-1/1/{(counter % 4) + 1}"
            cpu_util = message['data']['openconfig-platform:components']['component'][2]['cpu']['openconfig-platform-cpu:utilization']['state']['instant']
            output_power = message['data']['openconfig-platform:components']['component'][0]['openconfig-platform-transceiver:transceiver']['physical-channels']['channel'][0]['state']['output-power']
            
            print(f"📊 [{timestamp}] 发送数据 #{counter}")
            print(f"   📱 设备: nokia-alu-1")
            print(f"   🔌 收发器: {transceiver_name}")
            print(f"   💻 CPU: {cpu_util}%")
            print(f"   📡 输出功率: {output_power} dBm")
            print(f"   🌈 频率: {message['data']['openconfig-platform:components']['component'][1]['openconfig-terminal-device:optical-channel']['state']['frequency']} MHz")
            print("-" * 60)
            
            time.sleep(10)  # 每10秒发送一次
            
        except Exception as e:
            print(f"❌ 发送数据失败: {e}")
            time.sleep(5)  # 失败后等待5秒重试

def main():
    """主函数"""
    
    try:
        send_optical_data()
    except KeyboardInterrupt:
        print("\n🛑 停止发送数据")
    except Exception as e:
        print(f"❌ 程序运行失败: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    import sys
    sys.exit(main())
