{"update": {"timestamp": "1754881233747000000", "prefix": {"elem": [{"name": "openconfig-platform:components"}]}, "update": [{"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OLP-1-3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-4-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}]}}
{"update": {"timestamp": "1754881234747000000", "prefix": {"elem": [{"name": "openconfig-platform:components"}]}, "update": [{"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OLP-1-3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-4-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}]}}
{"update": {"timestamp": "1754881235747000000", "prefix": {"elem": [{"name": "openconfig-platform:components"}]}, "update": [{"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OLP-1-3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-4-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}]}}
{"update": {"timestamp": "1754881236747000000", "prefix": {"elem": [{"name": "openconfig-platform:components"}]}, "update": [{"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OLP-1-3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-4-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}]}}
{"update": {"timestamp": "1754881237747000000", "prefix": {"elem": [{"name": "openconfig-platform:components"}]}, "update": [{"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OLP-1-3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-4-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}]}}
{"update": {"timestamp": "1754881238747000000", "prefix": {"elem": [{"name": "openconfig-platform:components"}]}, "update": [{"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OLP-1-3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-4-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}]}}
{"update": {"timestamp": "1754881239747000000", "prefix": {"elem": [{"name": "openconfig-platform:components"}]}, "update": [{"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OLP-1-3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-4-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}]}}
{"update": {"timestamp": "1754881240747000000", "prefix": {"elem": [{"name": "openconfig-platform:components"}]}, "update": [{"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OLP-1-3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-4-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}]}}
{"update": {"timestamp": "1754881241747000000", "prefix": {"elem": [{"name": "openconfig-platform:components"}]}, "update": [{"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OLP-1-3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-4-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}]}}
{"update": {"timestamp": "1754881242747000000", "prefix": {"elem": [{"name": "openconfig-platform:components"}]}, "update": [{"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OLP-1-3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-4-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}]}}
{"update": {"timestamp": "1754881243747000000", "prefix": {"elem": [{"name": "openconfig-platform:components"}]}, "update": [{"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OLP-1-3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-4-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}]}}
{"update": {"timestamp": "1754881244747000000", "prefix": {"elem": [{"name": "openconfig-platform:components"}]}, "update": [{"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OLP-1-3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-4-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}]}}
{"update": {"timestamp": "1754881245747000000", "prefix": {"elem": [{"name": "openconfig-platform:components"}]}, "update": [{"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OLP-1-3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-4-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}]}}
{"update": {"timestamp": "1754881246747000000", "prefix": {"elem": [{"name": "openconfig-platform:components"}]}, "update": [{"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OLP-1-3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-4-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}]}}
{"update": {"timestamp": "1754881247747000000", "prefix": {"elem": [{"name": "openconfig-platform:components"}]}, "update": [{"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OLP-1-3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-4-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}]}}
{"update": {"timestamp": "1754881248747000000", "prefix": {"elem": [{"name": "openconfig-platform:components"}]}, "update": [{"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OLP-1-3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-4-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}]}}
{"update": {"timestamp": "1754881249747000000", "prefix": {"elem": [{"name": "openconfig-platform:components"}]}, "update": [{"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OLP-1-3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-4-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}]}}
