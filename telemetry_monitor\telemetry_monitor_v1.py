#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实时监控设备telemetry数据并记录到JSON文件
每个设备使用单独的线程和文件
"""

import json
import time
import threading
import logging
import socket
import struct
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional
from concurrent.futures import ThreadPoolExecutor
import sys

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(threadName)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class TelemetryConfig:
    """Telemetry配置类"""
    def __init__(self):
        # 从XML配置中提取的信息
        self.collector_ip = "***************"
        self.collector_port = 50051
        self.device_ip = "***************" 
        self.subscription_name = "allPsg"
        self.sensor_group = "allSg"
        
        # TCP接收器配置 - 用于接收设备推送的telemetry数据
        self.listen_ip = "0.0.0.0"  # 监听所有接口
        self.listen_port = 50051    # 监听端口，与collector_port一致
        
        # gRPC连接配置（如果需要主动连接设备）
        self.grpc_server = f"{self.device_ip}:57400"
        self.username = "admin"
        self.password = "admin"
        self.encoding = "JSON_IETF"  # 或者 "GPB"
        
        # 监控的路径
        self.sensor_paths = [
            "/openconfig-terminal-device:terminal-device/logical-channels/channel/otn/state",
            "/openconfig-terminal-device:terminal-device/logical-channels/channel/ethernet/state",
            "/openconfig-platform:components/component/openconfig-platform-transceiver:transceiver/physical-channels/channel/state",
            "/openconfig-platform:components/component/state", 
            "/openconfig-platform:components/component/cpu/openconfig-platform-cpu:utilization",
            "/openconfig-platform:components/component/openconfig-terminal-device:optical-channel/state",
            "/openconfig-platform:components/component/openconfig-platform-transceiver:transceiver/state",
            "/openconfig-platform:components/component/openconfig-transport-line-common:optical-port/state"
        ]


class TelemetryReceiver:
    """Telemetry数据接收器"""
    
    def __init__(self, config: TelemetryConfig, callback):
        self.config = config
        self.callback = callback
        self.server_socket = None
        self.running = False
        self.thread = None
        
    def start_receiver(self):
        """启动telemetry接收器"""
        if self.running:
            logger.warning("Telemetry接收器已在运行")
            return False
            
        try:
            # 创建TCP服务器套接字
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.server_socket.bind((self.config.listen_ip, self.config.listen_port))
            self.server_socket.listen(5)
            
            self.running = True
            self.thread = threading.Thread(target=self._receiver_loop, name="TelemetryReceiver")
            self.thread.daemon = True
            self.thread.start()
            
            logger.info(f"Telemetry接收器已启动，监听 {self.config.listen_ip}:{self.config.listen_port}")
            return True
            
        except Exception as e:
            logger.error(f"启动Telemetry接收器失败: {e}")
            return False
            
    def stop_receiver(self):
        """停止telemetry接收器"""
        self.running = False
        
        if self.server_socket:
            try:
                self.server_socket.close()
            except:
                pass
                
        if self.thread and self.thread.is_alive():
            self.thread.join(timeout=5)
            
        logger.info("Telemetry接收器已停止")
        
    def _receiver_loop(self):
        """接收器主循环"""
        logger.info("Telemetry接收器循环开始")
        
        while self.running:
            try:
                # 等待客户端连接
                client_socket, client_addr = self.server_socket.accept()
                logger.info(f"接收到来自 {client_addr} 的连接")
                
                # 为每个连接创建处理线程
                client_thread = threading.Thread(
                    target=self._handle_client,
                    args=(client_socket, client_addr),
                    name=f"Client-{client_addr[0]}"
                )
                client_thread.daemon = True
                client_thread.start()
                
            except Exception as e:
                if self.running:
                    logger.error(f"接收连接出错: {e}")
                    time.sleep(1)
                    
        logger.info("Telemetry接收器循环结束")
        
    def _handle_client(self, client_socket, client_addr):
        """处理客户端连接"""
        logger.info(f"开始处理来自 {client_addr} 的数据流")
        
        try:
            buffer = b''
            
            while self.running:
                try:
                    # 接收数据
                    data = client_socket.recv(4096)
                    if not data:
                        logger.info(f"客户端 {client_addr} 断开连接")
                        break
                        
                    buffer += data
                    
                    # 处理缓冲区中的完整消息
                    while buffer:
                        message, buffer = self._extract_message(buffer)
                        if message:
                            telemetry_data = self._parse_telemetry_message(message, client_addr[0])
                            if telemetry_data:
                                self.callback(telemetry_data)
                        else:
                            break
                            
                except socket.timeout:
                    continue
                except Exception as e:
                    logger.error(f"处理客户端 {client_addr} 数据出错: {e}")
                    break
                    
        except Exception as e:
            logger.error(f"客户端连接处理出错: {e}")
        finally:
            try:
                client_socket.close()
            except:
                pass
            logger.info(f"客户端 {client_addr} 连接已关闭")
            
    def _extract_message(self, buffer):
        """从缓冲区提取完整的消息"""
        # 这里需要根据实际的telemetry协议来解析消息边界
        # 常见的方式包括：
        # 1. 固定长度头部 + 变长消息体
        # 2. 分隔符分隔的消息
        # 3. 自描述的协议（如gRPC）
        
        # 示例：假设使用长度前缀协议（4字节长度 + 消息体）
        if len(buffer) < 4:
            return None, buffer
            
        try:
            # 读取消息长度（大端序）
            msg_length = struct.unpack('>I', buffer[:4])[0]
            
            if len(buffer) < 4 + msg_length:
                # 消息不完整
                return None, buffer
                
            # 提取完整消息
            message = buffer[4:4 + msg_length]
            remaining_buffer = buffer[4 + msg_length:]
            
            return message, remaining_buffer
            
        except Exception as e:
            logger.error(f"解析消息长度出错: {e}")
            # 如果解析失败，尝试查找JSON消息边界
            return self._extract_json_message(buffer)
            
    def _extract_json_message(self, buffer):
        """尝试从缓冲区提取JSON消息"""
        try:
            # 尝试解码为文本
            text = buffer.decode('utf-8')
            
            # 查找JSON消息边界
            brace_count = 0
            start_pos = -1
            
            for i, char in enumerate(text):
                if char == '{':
                    if start_pos == -1:
                        start_pos = i
                    brace_count += 1
                elif char == '}':
                    brace_count -= 1
                    if brace_count == 0 and start_pos != -1:
                        # 找到完整的JSON消息
                        json_msg = text[start_pos:i+1]
                        remaining = text[i+1:].encode('utf-8')
                        return json_msg.encode('utf-8'), remaining
                        
            # 没有找到完整的JSON消息
            return None, buffer
            
        except UnicodeDecodeError:
            # 不是文本数据，可能是二进制protobuf
            logger.debug("接收到二进制数据，可能是protobuf格式")
            return None, buffer
            
    def _parse_telemetry_message(self, message, device_ip):
        """解析telemetry消息"""
        try:
            current_time = datetime.now()
            
            # 尝试解析为JSON
            try:
                json_data = json.loads(message.decode('utf-8'))
                
                # 构建标准化的telemetry数据结构
                telemetry_data = {
                    "timestamp": current_time.isoformat(),
                    "device_id": device_ip,
                    "raw_message": json_data,
                    "message_type": "json"
                }
                
                logger.debug(f"接收到来自 {device_ip} 的JSON telemetry数据")
                return telemetry_data
                
            except json.JSONDecodeError:
                # 可能是protobuf或其他二进制格式
                telemetry_data = {
                    "timestamp": current_time.isoformat(),
                    "device_id": device_ip,
                    "raw_message": message.hex(),  # 转换为十六进制字符串
                    "message_type": "binary",
                    "message_length": len(message)
                }
                
                logger.debug(f"接收到来自 {device_ip} 的二进制 telemetry数据，长度: {len(message)}")
                return telemetry_data
                
        except Exception as e:
            logger.error(f"解析telemetry消息出错: {e}")
            return None


class DeviceMonitor:
    """单个设备监控器"""
    
    def __init__(self, device_id: str, config: TelemetryConfig, output_dir: str = "telemetry_data"):
        self.device_id = device_id
        self.config = config
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # 为每个设备创建单独的JSON文件
        tmp_datatime = datetime.now().strftime('%Y%m%d%H%M%S')
        self.json_file = self.output_dir / f"{device_id}_telemetry_{tmp_datatime}.json"
        self.running = False
        self.data_buffer = []
        self.buffer_lock = threading.Lock()
        self.message_count = 0
        
    def start_monitoring(self):
        """启动监控"""
        if self.running:
            logger.warning(f"设备 {self.device_id} 监控已在运行")
            return
            
        self.running = True
        logger.info(f"启动设备 {self.device_id} 的监控")
        
    def stop_monitoring(self):
        """停止监控"""
        self.running = False
        # 写入剩余缓冲区数据
        self._write_buffer_to_file()
        logger.info(f"停止设备 {self.device_id} 的监控")
        
    def on_telemetry_data_received(self, telemetry_data):
        """接收到telemetry数据的回调函数"""
        if telemetry_data and self.running:
            self.message_count += 1
            self._process_telemetry_data(telemetry_data)
            
            # 每收到10条消息打印一次统计
            if self.message_count % 10 == 0:
                logger.info(f"设备 {self.device_id} 已接收 {self.message_count} 条telemetry消息")
                
    def _process_telemetry_data(self, data: Dict[str, Any]):
        """处理telemetry数据"""
        with self.buffer_lock:
            self.data_buffer.append(data)
            
        # 如果缓冲区达到一定大小，立即写入
        if len(self.data_buffer) >= 5:  # 每5条消息写入一次
            self._write_buffer_to_file()
            
    def _write_buffer_to_file(self):
        """将缓冲区数据写入JSON文件"""
        if not self.data_buffer:
            return
            
        with self.buffer_lock:
            data_to_write = self.data_buffer.copy()
            self.data_buffer.clear()
            
        try:
            # 使用追加模式写入，每行一个JSON对象（JSON Lines格式）
            with open(self.json_file, 'a', encoding='utf-8') as f:
                for data in data_to_write:
                    json_line = json.dumps(data, ensure_ascii=False, separators=(',', ':'))
                    f.write(json_line + '\n')
                    
            logger.debug(f"写入 {len(data_to_write)} 条数据到 {self.json_file}")
            
        except Exception as e:
            logger.error(f"写入文件失败 {self.json_file}: {e}")
            # 如果写入失败，将数据放回缓冲区
            with self.buffer_lock:
                self.data_buffer = data_to_write + self.data_buffer
                
    def get_stats(self):
        """获取统计信息"""
        return {
            "message_count": self.message_count,
            "buffer_size": len(self.data_buffer),
            "json_file": str(self.json_file),
            "file_size": self.json_file.stat().st_size if self.json_file.exists() else 0
        }


class TelemetryMonitorManager:
    """Telemetry监控管理器"""
    
    def __init__(self, config: TelemetryConfig):
        self.config = config
        self.device_monitors: Dict[str, DeviceMonitor] = {}
        self.telemetry_receiver = None
        self.running = False
        
    def add_device(self, device_id: str):
        """添加设备监控"""
        if device_id in self.device_monitors:
            logger.warning(f"设备 {device_id} 已存在")
            return
            
        monitor = DeviceMonitor(device_id, self.config)
        self.device_monitors[device_id] = monitor
        
        if self.running:
            monitor.start_monitoring()
            
        logger.info(f"添加设备 {device_id} 到监控列表")
        
    def remove_device(self, device_id: str):
        """移除设备监控"""
        if device_id not in self.device_monitors:
            logger.warning(f"设备 {device_id} 不存在")
            return
            
        monitor = self.device_monitors[device_id]
        monitor.stop_monitoring()
        del self.device_monitors[device_id]
        
        logger.info(f"从监控列表移除设备 {device_id}")
        
    def start_all_monitoring(self):
        """启动所有设备监控和telemetry接收器"""
        if self.running:
            logger.warning("监控已在运行")
            return False
            
        # 启动telemetry接收器
        self.telemetry_receiver = TelemetryReceiver(self.config, self._on_telemetry_data_received)
        if not self.telemetry_receiver.start_receiver():
            logger.error("启动telemetry接收器失败")
            return False
            
        self.running = True
        
        # 启动所有设备监控
        for device_id, monitor in self.device_monitors.items():
            monitor.start_monitoring()
            
        logger.info(f"启动所有设备监控，共 {len(self.device_monitors)} 个设备")
        return True
        
    def stop_all_monitoring(self):
        """停止所有设备监控和telemetry接收器"""
        self.running = False
        
        # 停止telemetry接收器
        if self.telemetry_receiver:
            self.telemetry_receiver.stop_receiver()
            self.telemetry_receiver = None
        
        # 停止所有设备监控
        for device_id, monitor in self.device_monitors.items():
            monitor.stop_monitoring()
            
        logger.info("停止所有设备监控")
        
    def _on_telemetry_data_received(self, telemetry_data):
        """接收到telemetry数据的回调函数"""
        device_id = telemetry_data.get("device_id")
        
        if device_id in self.device_monitors:
            # 转发给对应的设备监控器
            self.device_monitors[device_id].on_telemetry_data_received(telemetry_data)
        else:
            # 如果是新设备，自动添加监控
            logger.info(f"检测到新设备 {device_id}，自动添加监控")
            self.add_device(device_id)
            self.device_monitors[device_id].on_telemetry_data_received(telemetry_data)
        
    def get_status(self) -> Dict[str, Any]:
        """获取监控状态"""
        status = {
            "running": self.running,
            "device_count": len(self.device_monitors),
            "receiver_running": self.telemetry_receiver is not None and self.telemetry_receiver.running,
            "listen_address": f"{self.config.listen_ip}:{self.config.listen_port}",
            "devices": {}
        }
        
        total_messages = 0
        total_buffer_size = 0
        
        for device_id, monitor in self.device_monitors.items():
            device_stats = monitor.get_stats()
            status["devices"][device_id] = {
                "running": monitor.running,
                "message_count": device_stats["message_count"],
                "buffer_size": device_stats["buffer_size"],
                "json_file": device_stats["json_file"],
                "file_size_bytes": device_stats["file_size"]
            }
            
            total_messages += device_stats["message_count"]
            total_buffer_size += device_stats["buffer_size"]
            
        status["total_messages"] = total_messages
        status["total_buffer_size"] = total_buffer_size
            
        return status


def main():
    """主函数"""
    # 创建配置
    config = TelemetryConfig()
    
    print(f"=== Telemetry数据接收器配置 ===")
    print(f"监听地址: {config.listen_ip}:{config.listen_port}")
    print(f"目标设备: {config.device_ip}")
    print(f"订阅名称: {config.subscription_name}")
    print(f"数据编码: {config.encoding}")
    print(f"监控路径数量: {len(config.sensor_paths)}")
    print("="*50)
    
    # 创建监控管理器
    manager = TelemetryMonitorManager(config)
    
    # 预先添加已知设备（可选）
    # 也可以等设备主动发送数据时自动添加
    known_devices = [
        config.device_ip,  # 主设备
        # 可以在这里预先添加更多已知设备
    ]
    
    for device_id in known_devices:
        manager.add_device(device_id)
    
    try:
        # 启动监控和接收器
        if not manager.start_all_monitoring():
            print("启动监控失败，退出程序")
            return
        
        print("Telemetry数据接收器已启动...")
        print("等待设备发送telemetry数据...")
        print(f"数据将保存到 telemetry_data/ 目录")
        print("按 Ctrl+C 停止监控")
        print("-" * 50)
        
        # 定期打印状态
        while True:
            time.sleep(30)  # 每30秒打印一次状态
            status = manager.get_status()
            current_time = datetime.now().strftime('%H:%M:%S')
            
            print(f"\n=== 监控状态 [{current_time}] ===")
            print(f"接收器运行: {status['receiver_running']}")
            print(f"监听地址: {status['listen_address']}")
            print(f"设备数量: {status['device_count']}")
            print(f"总消息数: {status['total_messages']}")
            print(f"总缓冲区: {status['total_buffer_size']}")
            
            if status["devices"]:
                print("\n设备详情:")
                for device_id, device_status in status["devices"].items():
                    file_size_mb = device_status['file_size_bytes'] / 1024 / 1024
                    print(f"  {device_id}: 消息={device_status['message_count']}, "
                          f"缓冲区={device_status['buffer_size']}, "
                          f"文件大小={file_size_mb:.2f}MB")
            else:
                print("  暂无设备数据")
                      
    except KeyboardInterrupt:
        print("\n收到停止信号...")
        manager.stop_all_monitoring()
        print("监控已停止")
        print("数据文件已保存在 telemetry_data/ 目录中")
        
        # 打印最终统计
        final_status = manager.get_status()
        if final_status["total_messages"] > 0:
            print(f"\n=== 最终统计 ===")
            print(f"总共接收消息: {final_status['total_messages']}")
            for device_id, device_status in final_status["devices"].items():
                print(f"  {device_id}: {device_status['message_count']} 条消息")


if __name__ == "__main__":
    main()
