# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: telemetry_service.proto
# Protobuf Python Version: 6.31.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    1,
    '',
    'telemetry_service.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x17telemetry_service.proto\x12\ttelemetry\"\x93\x01\n\x1cTelemetrySubscriptionRequest\x12\x0f\n\x07node_id\x18\x01 \x01(\t\x12\x17\n\x0fsubscription_id\x18\x02 \x01(\t\x12\x14\n\x0csensor_paths\x18\x03 \x03(\t\x12\x17\n\x0fsample_interval\x18\x04 \x01(\x05\x12\x1a\n\x12heartbeat_interval\x18\x05 \x01(\x05\"t\n\x0fTelemetryUpdate\x12\x0f\n\x07node_id\x18\x01 \x01(\t\x12\x17\n\x0fsubscription_id\x18\x02 \x01(\t\x12\x13\n\x0bsensor_path\x18\x03 \x01(\t\x12\x11\n\ttimestamp\x18\x04 \x01(\x03\x12\x0f\n\x07payload\x18\x05 \x01(\x0c\"\x91\x01\n\x08KeyValue\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x16\n\x0cstring_value\x18\x02 \x01(\tH\x00\x12\x13\n\tint_value\x18\x03 \x01(\x03H\x00\x12\x15\n\x0b\x66loat_value\x18\x04 \x01(\x01H\x00\x12\x14\n\nbool_value\x18\x05 \x01(\x08H\x00\x12\x15\n\x0b\x62ytes_value\x18\x06 \x01(\x0cH\x00\x42\x07\n\x05value2o\n\x10TelemetryService\x12[\n\x12telemetrySubscribe\x12\'.telemetry.TelemetrySubscriptionRequest\x1a\x1a.telemetry.TelemetryUpdate0\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'telemetry_service_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  DESCRIPTOR._loaded_options = None
  _globals['_TELEMETRYSUBSCRIPTIONREQUEST']._serialized_start=39
  _globals['_TELEMETRYSUBSCRIPTIONREQUEST']._serialized_end=186
  _globals['_TELEMETRYUPDATE']._serialized_start=188
  _globals['_TELEMETRYUPDATE']._serialized_end=304
  _globals['_KEYVALUE']._serialized_start=307
  _globals['_KEYVALUE']._serialized_end=452
  _globals['_TELEMETRYSERVICE']._serialized_start=454
  _globals['_TELEMETRYSERVICE']._serialized_end=565
# @@protoc_insertion_point(module_scope)
