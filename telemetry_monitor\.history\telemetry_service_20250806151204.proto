syntax = "proto3";

package telemetry;

service TelemetryService {
    rpc telemetrySubscribe (TelemetrySubscriptionRequest) returns (stream TelemetryUpdate);
}

message TelemetrySubscriptionRequest {
    string node_id = 1;
    string subscription_id = 2;
    repeated string sensor_paths = 3;
    int32 sample_interval = 4;
    int32 heartbeat_interval = 5;
}

message TelemetryUpdate {
    string node_id = 1;
    string subscription_id = 2;
    string sensor_path = 3;
    int64 timestamp = 4;
    bytes payload = 5;
}

message KeyValue {
    string key = 1;
    oneof value {
        string string_value = 2;
        int64 int_value = 3;
        double float_value = 4;
        bool bool_value = 5;
        bytes bytes_value = 6;
    }
}
