syntax = "proto3";

package gnmi_dialout;

option go_package = "github.com/openconfig/gnmi/proto/gnmi_dialout;gnmi_dialout";
option java_package = "com.github.openconfig.gnmi.proto.dialout";
option java_outer_classname = "GnmiDialout";

import "google/protobuf/any.proto";
import "google/protobuf/descriptor.proto";
import "tmp_proto/gnmi_base.proto"; // Import the base gNMI messages

// The gNMI Dialout service definition for Nokia devices.
service gNMIDialout {
  // Publish is a streaming RPC that allows a target to stream telemetry
  // notifications to a collector.
  rpc Publish(stream gnmi.SubscribeResponse) returns (gnmi.PublishResponse) {}
}
