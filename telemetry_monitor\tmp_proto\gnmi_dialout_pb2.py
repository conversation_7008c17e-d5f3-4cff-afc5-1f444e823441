# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: tmp_proto/gnmi_dialout.proto
# Protobuf Python Version: 6.31.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    1,
    '',
    'tmp_proto/gnmi_dialout.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import any_pb2 as google_dot_protobuf_dot_any__pb2
from google.protobuf import descriptor_pb2 as google_dot_protobuf_dot_descriptor__pb2
from tmp_proto import gnmi_base_pb2 as tmp__proto_dot_gnmi__base__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1ctmp_proto/gnmi_dialout.proto\x12\x0cgnmi_dialout\x1a\x19google/protobuf/any.proto\x1a google/protobuf/descriptor.proto\x1a\x19tmp_proto/gnmi_base.proto2L\n\x0bgNMIDialout\x12=\n\x07Publish\x12\x17.gnmi.SubscribeResponse\x1a\x15.gnmi.PublishResponse\"\x00(\x01\x42s\n(com.github.openconfig.gnmi.proto.dialoutB\x0bGnmiDialoutZ:github.com/openconfig/gnmi/proto/gnmi_dialout;gnmi_dialoutb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'tmp_proto.gnmi_dialout_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n(com.github.openconfig.gnmi.proto.dialoutB\013GnmiDialoutZ:github.com/openconfig/gnmi/proto/gnmi_dialout;gnmi_dialout'
  _globals['_GNMIDIALOUT']._serialized_start=134
  _globals['_GNMIDIALOUT']._serialized_end=210
# @@protoc_insertion_point(module_scope)
