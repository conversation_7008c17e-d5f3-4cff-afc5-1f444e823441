import grpc
import json
import threading
import logging
import signal
import os
import time
from datetime import datetime
from queue import Queue
from contextlib import contextmanager

# Import generated gRPC code
import telemetry_service_pb2 as telemetry_pb2
import telemetry_service_pb2_grpc as telemetry_pb2_grpc

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Global flag for graceful shutdown
running = True

# Signal handler for graceful shutdown
def signal_handler(signum, frame):
    global running
    logger.info("Received shutdown signal, stopping threads...")
    running = False

signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)

# Buffer for batch writing
class TelemetryBuffer:
    def __init__(self, filename, max_buffer_size=100):
        self.filename = filename
        self.buffer = []
        self.max_buffer_size = max_buffer_size
        self.lock = threading.Lock()
        
    def add(self, data):
        with self.lock:
            self.buffer.append(data)
            if len(self.buffer) >= self.max_buffer_size:
                self.flush()
                
    def flush(self):
        with self.lock:
            if self.buffer:
                with open(self.filename, "a") as f:
                    for data in self.buffer:
                        f.write(json.dumps(data) + "\n")
                self.buffer.clear()

@contextmanager
def grpc_channel_manager(address, port, timeout=5):
    """Context manager for gRPC channel with reconnection logic"""
    channel = None
    try:
        channel = grpc.insecure_channel(f"{address}:{port}")
        yield channel
    except Exception as e:
        logger.error(f"Channel error: {e}")
        raise
    finally:
        if channel:
            channel.close()

# Define a function to handle telemetry data for a single device
def monitor_device(device_config):
    """
    Connects to the telemetry server and monitors device data.

    :param device_config: Dictionary containing device configuration
    """
    device_address = device_config["address"]
    server_address = device_config["server_address"]
    server_port = device_config["port"]
    output_file = device_config["output_file"]
    subscription_id = device_config["subscription_id"]
    sensor_paths = device_config["sensor_paths"]
    
    logger.info(f"Starting monitoring for device {device_address}")
    buffer = TelemetryBuffer(output_file)
    retry_count = 0
    max_retries = 3

    while running and retry_count < max_retries:
        try:
            with grpc_channel_manager(server_address, server_port) as channel:
                # Create gRPC metadata
                metadata = [
                    ('username', 'telemetry'),
                    ('subscription-id', subscription_id),
                    ('node-id', device_address)
                ]

                # Create gRPC stub
                stub = telemetry_pb2_grpc.TelemetryServiceStub(channel)
                
                # Create subscription request
                request = telemetry_pb2.TelemetrySubscriptionRequest(
                    subscription_id=subscription_id,
                    node_id=device_address,
                    sensor_paths=sensor_paths,
                    sample_interval=device_config["sample_interval"],
                    heartbeat_interval=device_config["heartbeat_interval"]
                )
                
                # Start streaming telemetry data
                logger.info(f"Starting telemetry stream for {device_address}")
                stream = stub.telemetrySubscribe(request, metadata=metadata)
                
                while running:
                    try:
                        response = next(stream)
                        
                        # Parse telemetry data
                        telemetry_data = {
                            "timestamp": datetime.fromtimestamp(response.timestamp / 1000).isoformat(),
                            "device": response.node_id,
                            "subscription": response.subscription_id,
                            "path": response.sensor_path,
                            "data": None
                        }
                        
                        # Parse telemetry response data
                        try:
                            if response.payload:
                                import google.protobuf.json_format as json_format
                                data_dict = json_format.MessageToDict(response.payload)
                                telemetry_data["data"] = data_dict
                        except Exception as parse_error:
                            logger.warning(f"Failed to parse payload: {parse_error}")
                            telemetry_data["data"] = response.payload.hex() if response.payload else None
                            
                        # Log received data
                        logger.info(f"Received telemetry data from {device_address} for path {telemetry_data['path']}")
                        
                        # Store the telemetry data
                        buffer.add(telemetry_data)
                        
                    except grpc.RpcError as rpc_error:
                        logger.error(f"RPC Error for device {device_address}: {rpc_error}")
                        break
                    except StopIteration:
                        logger.info(f"Telemetry stream ended for {device_address}")
                        break
                    except Exception as e:
                        logger.error(f"Error processing telemetry data from {device_address}: {e}")
                        continue
                        
        except Exception as e:
            retry_count += 1
            logger.error(f"Error monitoring device {device_address} (attempt {retry_count}/{max_retries}): {e}")
            if retry_count < max_retries:
                logger.info(f"Retrying connection to {device_address} in 5 seconds...")
                time.sleep(5)
                
    # Final flush of any remaining data
    buffer.flush()
    logger.info(f"Stopped monitoring device {device_address}")

# List of devices to monitor
SENSOR_PATHS = [
    "/openconfig-terminal-device:terminal-device/logical-channels/channel/ethernet/state",
    "/openconfig-platform:components/component/state",
    "/openconfig-terminal-device:terminal-device/logical-channels/channel/otn/state",
    "/openconfig-platform:components/component/openconfig-platform-transceiver:transceiver/physical-channels/channel/state",
    "/openconfig-platform:components/component/openconfig-terminal-device:optical-channel/state",
    "/openconfig-platform:components/component/openconfig-platform-transceiver:transceiver/state",
    "/openconfig-platform:components/component/cpu/openconfig-platform-cpu:utilization"
]

devices = [
    {
        "address": "***************",         # Device sending telemetry
        "server_address": "0.0.0.0",          # Listen on all interfaces
        "port": 50051,                        # gRPC server port for listening
        "output_file": "telemetry_data/device_***************.json",
        "subscription_id": "allPsg",
        "sample_interval": 1000,
        "heartbeat_interval": 15000,
        "sensor_paths": SENSOR_PATHS
    }
]

# Create telemetry_data directory if it doesn't exist
os.makedirs("telemetry_data", exist_ok=True)

# Create and start a thread for each device
threads = []
for device in devices:
    t = threading.Thread(
        target=monitor_device,
        args=(device,)  # Pass the entire device configuration
    )
    threads.append(t)
    t.start()

# Wait for all threads to complete
try:
    for t in threads:
        t.join()
except KeyboardInterrupt:
    logger.info("Received keyboard interrupt, initiating shutdown...")
    running = False
    # Wait again for threads to finish
    for t in threads:
        t.join()

logger.info("All monitoring threads have stopped. Exiting...")
