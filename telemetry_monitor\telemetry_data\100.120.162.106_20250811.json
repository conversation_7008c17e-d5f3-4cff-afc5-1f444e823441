{"update": {"timestamp": "1754881234902000000", "prefix": {"elem": [{"name": "openconfig-platform:components"}]}, "update": [{"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}]}}
{"update": {"timestamp": "1754881235902000000", "prefix": {"elem": [{"name": "openconfig-platform:components"}]}, "update": [{"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}]}}
{"update": {"timestamp": "1754881236902000000", "prefix": {"elem": [{"name": "openconfig-platform:components"}]}, "update": [{"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}]}}
{"update": {"timestamp": "1754881237902000000", "prefix": {"elem": [{"name": "openconfig-platform:components"}]}, "update": [{"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}]}}
{"update": {"timestamp": "1754881238902000000", "prefix": {"elem": [{"name": "openconfig-platform:components"}]}, "update": [{"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}]}}
{"update": {"timestamp": "1754881239902000000", "prefix": {"elem": [{"name": "openconfig-platform:components"}]}, "update": [{"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}]}}
{"update": {"timestamp": "1754881240902000000", "prefix": {"elem": [{"name": "openconfig-platform:components"}]}, "update": [{"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}]}}
{"update": {"timestamp": "1754881241902000000", "prefix": {"elem": [{"name": "openconfig-platform:components"}]}, "update": [{"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}]}}
{"update": {"timestamp": "1754881242902000000", "prefix": {"elem": [{"name": "openconfig-platform:components"}]}, "update": [{"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}]}}
{"update": {"timestamp": "1754881243902000000", "prefix": {"elem": [{"name": "openconfig-platform:components"}]}, "update": [{"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}]}}
{"update": {"timestamp": "1754881244902000000", "prefix": {"elem": [{"name": "openconfig-platform:components"}]}, "update": [{"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}]}}
{"update": {"timestamp": "1754881245902000000", "prefix": {"elem": [{"name": "openconfig-platform:components"}]}, "update": [{"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}]}}
{"update": {"timestamp": "1754881246902000000", "prefix": {"elem": [{"name": "openconfig-platform:components"}]}, "update": [{"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}]}}
{"update": {"timestamp": "1754881247902000000", "prefix": {"elem": [{"name": "openconfig-platform:components"}]}, "update": [{"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}]}}
{"update": {"timestamp": "1754881248902000000", "prefix": {"elem": [{"name": "openconfig-platform:components"}]}, "update": [{"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}]}}
{"update": {"timestamp": "1754881249902000000", "prefix": {"elem": [{"name": "openconfig-platform:components"}]}, "update": [{"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}]}}
