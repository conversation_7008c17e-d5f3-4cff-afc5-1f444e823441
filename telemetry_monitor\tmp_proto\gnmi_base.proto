syntax = "proto3";

package gnmi;

option go_package = "github.com/openconfig/gnmi/proto/gnmi;gnmi";
option java_package = "com.github.openconfig.gnmi.proto";
option java_outer_classname = "Gnmi";

import "google/protobuf/any.proto";
import "google/protobuf/descriptor.proto";

message Notification {
  int64 timestamp = 1;
  Path prefix = 2;
  string alias = 3;
  repeated Update update = 4;
  repeated Path delete = 5;
  bool atomic = 6;
}

message Update {
  Path path = 1;
  Value value = 2;
  uint32 duplicates = 3;
}

message TypedValue {
  oneof value {
    string string_val = 1;
    int64 int_val = 2;
    uint64 uint_val = 3;
    bool bool_val = 4;
    bytes bytes_val = 5;
    float float_val = 6;
    Decimal64 decimal_val = 7;
    LeaflistVal leaflist_val = 8;
    google.protobuf.Any any_val = 9;
    bytes json_val = 10;
    bytes json_ietf_val = 11;
    string ascii_val = 12;
    bytes proto_bytes = 13;
  }
}

message Path {
  repeated string element = 1 [deprecated=true];
  string origin = 2;
  repeated PathElem elem = 3;
  string target = 4;
}

message PathElem {
  string name = 1;
  map<string, string> key = 2;
}

message Value {
  bytes value = 1;
  Encoding type = 2;
}

message Error {
  uint32 code = 1;
  string message = 2;
  google.protobuf.Any data = 3;
}

message Decimal64 {
  int64 digits = 1;
  uint32 precision = 2;
}

message ScalarArray {
  repeated TypedValue element = 1;
}

enum Encoding {
  JSON = 0;
  BYTES = 1;
  PROTO = 2;
  ASCII = 3;
  JSON_IETF = 4;
}

message SubscribeResponse {
  oneof response {
    Notification update = 1;
    bool sync_response = 3;
    Error error = 4;
  }
}

message PublishResponse {}

message LeaflistVal {
  repeated TypedValue element = 1;
  ScalarArray scalar_array = 2 [deprecated=true];
}
