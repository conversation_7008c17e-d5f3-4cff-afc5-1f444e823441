#!/usr/bin/env python3
"""
测试收集器功能
发送模拟的 telemetry 数据到收集器
"""

import socket
import json
import time
import threading
from datetime import datetime

def send_test_data():
    """发送测试数据到收集器"""
    
    collector_ip = "***************"
    collector_port = 57400
    
    print(f"🧪 测试收集器连接: {collector_ip}:{collector_port}")
    
    try:
        # 连接到收集器
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        sock.connect((collector_ip, collector_port))
        
        print("✅ 成功连接到收集器")
        
        # 发送测试数据
        test_messages = [
            {
                "timestamp": int(time.time() * 1000),
                "device": "nokia-alu-1",
                "sensor_group": "system-monitoring",
                "data": {
                    "cpu_utilization": 45.2,
                    "memory_utilization": 67.8,
                    "uptime": 86400,
                    "temperature": 42.5
                }
            },
            {
                "timestamp": int(time.time() * 1000),
                "device": "nokia-alu-1", 
                "sensor_group": "interface-monitoring",
                "data": {
                    "interface": "1/1/1",
                    "in_octets": 1234567890,
                    "out_octets": 987654321,
                    "in_packets": 12345,
                    "out_packets": 9876,
                    "errors": 0
                }
            },
            {
                "timestamp": int(time.time() * 1000),
                "device": "nokia-alu-1",
                "sensor_group": "platform-monitoring", 
                "data": {
                    "chassis": {
                        "temperature": 38.5,
                        "power_consumption": 150.2,
                        "fan_speed": 3500
                    },
                    "linecard": {
                        "slot": 1,
                        "status": "active",
                        "temperature": 41.2
                    }
                }
            }
        ]
        
        for i, message in enumerate(test_messages, 1):
            json_data = json.dumps(message) + "\n"
            sock.send(json_data.encode('utf-8'))
            print(f"📤 发送测试消息 {i}: {message['sensor_group']}")
            time.sleep(2)
        
        # 发送一些原始文本数据
        raw_messages = [
            "NOKIA-ALU-TELEMETRY: CPU=45.2% MEM=67.8% TEMP=42.5C",
            "INTERFACE-STATS: 1/1/1 IN=1234567890 OUT=987654321",
            "SYSTEM-ALERT: High temperature detected on linecard 1"
        ]
        
        for i, message in enumerate(raw_messages, 1):
            sock.send((message + "\n").encode('utf-8'))
            print(f"📤 发送原始消息 {i}: {message[:50]}...")
            time.sleep(2)
        
        sock.close()
        print("✅ 测试数据发送完成")
        
    except Exception as e:
        print(f"❌ 发送测试数据失败: {e}")

def continuous_data_sender():
    """持续发送数据"""
    
    collector_ip = "***************"
    collector_port = 57400
    
    print(f"🔄 启动持续数据发送器: {collector_ip}:{collector_port}")
    
    counter = 0
    
    while True:
        try:
            counter += 1
            
            # 连接到收集器
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            sock.connect((collector_ip, collector_port))
            
            # 生成动态数据
            message = {
                "timestamp": int(time.time() * 1000),
                "device": "nokia-alu-1",
                "sensor_group": "continuous-monitoring",
                "sequence": counter,
                "data": {
                    "cpu_utilization": 40 + (counter % 20),
                    "memory_utilization": 60 + (counter % 30),
                    "interface_1_1_1": {
                        "in_octets": 1000000000 + counter * 1000,
                        "out_octets": 800000000 + counter * 800,
                        "utilization": 10 + (counter % 80)
                    },
                    "interface_1_1_2": {
                        "in_octets": 2000000000 + counter * 1500,
                        "out_octets": 1500000000 + counter * 1200,
                        "utilization": 15 + (counter % 70)
                    },
                    "system": {
                        "uptime": 86400 + counter * 60,
                        "temperature": 35 + (counter % 15),
                        "power": 140 + (counter % 20)
                    }
                }
            }
            
            json_data = json.dumps(message) + "\n"
            sock.send(json_data.encode('utf-8'))
            sock.close()
            
            print(f"📊 发送连续数据 #{counter} - CPU: {message['data']['cpu_utilization']}%")
            
            time.sleep(10)  # 每10秒发送一次
            
        except Exception as e:
            print(f"❌ 发送连续数据失败: {e}")
            time.sleep(5)  # 失败后等待5秒重试

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='测试收集器功能')
    parser.add_argument('mode', choices=['test', 'continuous'], 
                       help='模式: test=发送测试数据, continuous=持续发送数据')
    
    args = parser.parse_args()
    
    if args.mode == 'test':
        send_test_data()
    elif args.mode == 'continuous':
        try:
            continuous_data_sender()
        except KeyboardInterrupt:
            print("\n🛑 停止持续发送")
    
    return 0

if __name__ == "__main__":
    import sys
    sys.exit(main())
