#!/usr/bin/env python3
"""
取消设备 Telemetry 订阅脚本
用于清除设备上的 telemetry 配置
"""

import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(__file__))

from src.netconf_client import NetconfClient
from src.utils import setup_logging

def cancel_all_subscriptions():
    """取消所有 telemetry 订阅"""
    
    print("🚫 取消设备 Telemetry 订阅")
    print("=" * 50)
    
    try:
        # 初始化 NETCONF 客户端
        print("1. 初始化 NETCONF 客户端...")
        client = NetconfClient("config/device_config.yaml")
        print("✅ NETCONF 客户端初始化成功")
        
        device_name = "nokia-alu-1"
        
        # 测试设备连接
        print(f"\n2. 测试设备 '{device_name}' 连接...")
        if not client.validate_connection(device_name):
            print("❌ 设备连接失败")
            return False
        print("✅ 设备连接成功")
        
        # 获取当前配置
        print("\n3. 获取当前 telemetry 配置...")
        with client.get_connection(device_name) as conn:
            filter_xml = '''
            <telemetry-system xmlns="http://openconfig.net/yang/telemetry">
              <sensor-groups/>
              <destination-groups/>
              <subscriptions/>
            </telemetry-system>
            '''
            
            current_config = conn.get_config(source='running', filter=('subtree', filter_xml))
            config_str = str(current_config)
            
            # 检查是否有配置需要删除
            has_sensor_groups = 'sensor-group' in config_str
            has_destination_groups = 'destination-group' in config_str
            has_subscriptions = 'subscription' in config_str
            
            if not (has_sensor_groups or has_destination_groups or has_subscriptions):
                print("ℹ️  设备上没有找到 telemetry 配置")
                return True
            
            print("✅ 找到现有 telemetry 配置")
            if has_sensor_groups:
                print("   - 传感器组: 存在")
            if has_destination_groups:
                print("   - 目标组: 存在")
            if has_subscriptions:
                print("   - 订阅: 存在")
        
        # 删除配置
        print("\n4. 删除 telemetry 配置...")
        
        # 构建删除配置
        delete_config_parts = []
        
        if has_sensor_groups:
            delete_config_parts.append('''
        <sensor-groups>
          <sensor-group operation="delete">
            <sensor-group-id>allSg</sensor-group-id>
          </sensor-group>
        </sensor-groups>''')
        
        if has_destination_groups:
            delete_config_parts.append('''
        <destination-groups>
          <destination-group operation="delete">
            <group-id>optical-collector</group-id>
          </destination-group>
          <destination-group operation="delete">
            <group-id>test-collector</group-id>
          </destination-group>
          <destination-group operation="delete">
            <group-id>comprehensive-collector</group-id>
          </destination-group>
        </destination-groups>''')
        
        if has_subscriptions:
            delete_config_parts.append('''
        <subscriptions>
          <subscription operation="delete">
            <subscription-id>optical-monitoring</subscription-id>
          </subscription>
        </subscriptions>''')
        
        if delete_config_parts:
            delete_config = f'''
<config xmlns="urn:ietf:params:xml:ns:netconf:base:1.0">
  <telemetry-system xmlns="http://openconfig.net/yang/telemetry">
{''.join(delete_config_parts)}
  </telemetry-system>
</config>
'''
            
            # 保存删除配置到文件
            with open("delete_telemetry_config.xml", "w", encoding="utf-8") as f:
                f.write(delete_config)
            print("✅ 删除配置已保存到 delete_telemetry_config.xml")
            
            # 应用删除配置
            with client.get_connection(device_name) as conn:
                try:
                    result = conn.edit_config(target='running', config=delete_config)
                    print("✅ 删除配置已应用到设备")
                except Exception as e:
                    print(f"⚠️  部分配置删除失败: {e}")
                    # 尝试逐个删除
                    print("🔄 尝试逐个删除配置项...")
                    success_count = 0
                    
                    if has_sensor_groups:
                        try:
                            sensor_delete = f'''
<config xmlns="urn:ietf:params:xml:ns:netconf:base:1.0">
  <telemetry-system xmlns="http://openconfig.net/yang/telemetry">
    <sensor-groups>
      <sensor-group operation="delete">
        <sensor-group-id>allSg</sensor-group-id>
      </sensor-group>
    </sensor-groups>
  </telemetry-system>
</config>
'''
                            conn.edit_config(target='running', config=sensor_delete)
                            print("   ✅ 传感器组删除成功")
                            success_count += 1
                        except Exception as e:
                            print(f"   ❌ 传感器组删除失败: {e}")
                    
                    if has_destination_groups:
                        try:
                            dest_delete = f'''
<config xmlns="urn:ietf:params:xml:ns:netconf:base:1.0">
  <telemetry-system xmlns="http://openconfig.net/yang/telemetry">
    <destination-groups>
      <destination-group operation="delete">
        <group-id>optical-collector</group-id>
      </destination-group>
    </destination-groups>
  </telemetry-system>
</config>
'''
                            conn.edit_config(target='running', config=dest_delete)
                            print("   ✅ 目标组删除成功")
                            success_count += 1
                        except Exception as e:
                            print(f"   ❌ 目标组删除失败: {e}")
                    
                    if success_count > 0:
                        print(f"✅ 成功删除 {success_count} 个配置项")
        
        # 验证删除结果
        print("\n5. 验证删除结果...")
        with client.get_connection(device_name) as conn:
            final_config = conn.get_config(source='running', filter=('subtree', filter_xml))
            final_config_str = str(final_config)
            
            remaining_sensor_groups = 'allSg' in final_config_str
            remaining_destination_groups = 'optical-collector' in final_config_str or 'test-collector' in final_config_str
            remaining_subscriptions = 'optical-monitoring' in final_config_str
            
            if not (remaining_sensor_groups or remaining_destination_groups or remaining_subscriptions):
                print("✅ 所有 telemetry 配置已成功删除")
                
                # 保存最终配置状态
                with open("final_telemetry_config.xml", "w", encoding="utf-8") as f:
                    f.write(str(final_config))
                print("✅ 最终配置状态已保存到 final_telemetry_config.xml")
                
                return True
            else:
                print("⚠️  部分配置仍然存在:")
                if remaining_sensor_groups:
                    print("   - 传感器组: 仍存在")
                if remaining_destination_groups:
                    print("   - 目标组: 仍存在")
                if remaining_subscriptions:
                    print("   - 订阅: 仍存在")
                return False
        
    except Exception as e:
        print(f"❌ 操作失败: {e}")
        return False
    
    finally:
        try:
            client.disconnect_all()
        except:
            pass

def check_subscription_status():
    """检查订阅状态"""
    
    print("🔍 检查设备 Telemetry 订阅状态")
    print("=" * 50)
    
    try:
        client = NetconfClient("config/device_config.yaml")
        device_name = "nokia-alu-1"
        
        print(f"正在检查设备 '{device_name}' 的 telemetry 状态...")
        
        with client.get_connection(device_name) as conn:
            filter_xml = '''
            <telemetry-system xmlns="http://openconfig.net/yang/telemetry">
              <sensor-groups/>
              <destination-groups/>
              <subscriptions/>
            </telemetry-system>
            '''
            
            result = conn.get_config(source='running', filter=('subtree', filter_xml))
            config_str = str(result)
            
            print("\n📊 当前状态:")
            
            # 检查传感器组
            if 'sensor-group' in config_str:
                if 'allSg' in config_str:
                    print("   📡 传感器组 'allSg': ✅ 存在")
                else:
                    print("   📡 传感器组: ⚠️  存在其他组")
            else:
                print("   📡 传感器组: ❌ 不存在")
            
            # 检查目标组
            if 'destination-group' in config_str:
                groups = []
                if 'optical-collector' in config_str:
                    groups.append('optical-collector')
                if 'test-collector' in config_str:
                    groups.append('test-collector')
                if 'comprehensive-collector' in config_str:
                    groups.append('comprehensive-collector')
                
                if groups:
                    print(f"   🎯 目标组: ✅ 存在 ({', '.join(groups)})")
                else:
                    print("   🎯 目标组: ⚠️  存在其他组")
            else:
                print("   🎯 目标组: ❌ 不存在")
            
            # 检查订阅
            if 'subscription' in config_str:
                if 'optical-monitoring' in config_str:
                    print("   📋 订阅 'optical-monitoring': ✅ 存在")
                else:
                    print("   📋 订阅: ⚠️  存在其他订阅")
            else:
                print("   📋 订阅: ❌ 不存在")
            
            # 保存当前状态
            with open("current_subscription_status.xml", "w", encoding="utf-8") as f:
                f.write(str(result))
            print("\n✅ 当前状态已保存到 current_subscription_status.xml")
            
            return True
            
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False
    
    finally:
        try:
            client.disconnect_all()
        except:
            pass

def main():
    """主函数"""
    
    import argparse
    
    parser = argparse.ArgumentParser(description='取消设备 Telemetry 订阅')
    parser.add_argument('action', choices=['cancel', 'check'], 
                       help='操作类型: cancel=取消订阅, check=检查状态')
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logging("INFO")
    
    if args.action == 'cancel':
        success = cancel_all_subscriptions()
        if success:
            print("\n🎉 === 订阅取消成功 ===")
            print("✅ 设备上的 telemetry 配置已清除")
            print("ℹ️  现在设备不会再发送 telemetry 数据")
            return 0
        else:
            print("\n❌ === 订阅取消失败 ===")
            print("⚠️  请检查错误信息并重试")
            return 1
    
    elif args.action == 'check':
        success = check_subscription_status()
        if success:
            print("\n✅ === 状态检查完成 ===")
            return 0
        else:
            print("\n❌ === 状态检查失败 ===")
            return 1

if __name__ == "__main__":
    sys.exit(main())
