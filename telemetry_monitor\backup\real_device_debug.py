#!/usr/bin/env python3
"""
实际设备 Telemetry 调试脚本
使用收集器 IP: ***************
获取尽量多的设备信息并实时监控
"""

import sys
import os
import time
import threading
import json
from datetime import datetime

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(__file__))

from src.netconf_client import NetconfClient
from src.telemetry_config import TelemetryConfigurator
from src.utils import setup_logging

class RealTimeMonitor:
    """实时监控器"""
    
    def __init__(self, collector_ip="***************", collector_port=57400):
        self.collector_ip = collector_ip
        self.collector_port = collector_port
        self.running = False
        self.data_count = 0
        
    def start_monitoring(self):
        """启动实时监控"""
        print(f"=== 启动实时监控 ===")
        print(f"收集器地址: {self.collector_ip}:{self.collector_port}")
        print("按 Ctrl+C 停止监控\n")
        
        self.running = True
        
        # 启动数据接收线程
        monitor_thread = threading.Thread(target=self._monitor_data)
        monitor_thread.daemon = True
        monitor_thread.start()
        
        try:
            while self.running:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n停止监控...")
            self.running = False
    
    def _monitor_data(self):
        """监控数据接收"""
        import socket
        
        try:
            # 创建服务器套接字
            server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            server_socket.bind((self.collector_ip, self.collector_port))
            server_socket.listen(5)
            
            print(f"✓ 监控服务启动，监听 {self.collector_ip}:{self.collector_port}")
            
            while self.running:
                try:
                    client_socket, client_address = server_socket.accept()
                    print(f"\n🔗 新连接: {client_address}")
                    
                    # 处理客户端数据
                    self._handle_client(client_socket, client_address)
                    
                except Exception as e:
                    if self.running:
                        print(f"❌ 接受连接失败: {e}")
                
        except Exception as e:
            print(f"❌ 启动监控服务失败: {e}")
        finally:
            try:
                server_socket.close()
            except:
                pass
    
    def _handle_client(self, client_socket, client_address):
        """处理客户端数据"""
        buffer = ""
        
        try:
            while self.running:
                data = client_socket.recv(4096)
                if not data:
                    break
                
                try:
                    decoded_data = data.decode('utf-8')
                    buffer += decoded_data
                    
                    # 处理完整的消息
                    while '\n' in buffer:
                        line, buffer = buffer.split('\n', 1)
                        if line.strip():
                            self._process_telemetry_data(line.strip(), client_address)
                            
                except UnicodeDecodeError:
                    # 处理二进制数据
                    self._process_binary_data(data, client_address)
                
        except Exception as e:
            print(f"❌ 处理客户端数据失败: {e}")
        finally:
            try:
                client_socket.close()
                print(f"🔌 客户端 {client_address} 断开连接")
            except:
                pass
    
    def _process_telemetry_data(self, data, client_address):
        """处理 telemetry 数据"""
        self.data_count += 1
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
        
        print(f"\n📊 [{timestamp}] 数据 #{self.data_count} 来自 {client_address}")
        print("=" * 60)
        
        # 尝试解析 JSON 数据
        try:
            json_data = json.loads(data)
            print("📋 JSON 数据:")
            print(json.dumps(json_data, indent=2, ensure_ascii=False))
            
            # 提取关键信息
            self._extract_key_info(json_data)
            
        except json.JSONDecodeError:
            print("📝 原始数据:")
            print(data)
        
        print("=" * 60)
        
        # 保存数据
        self._save_data(data, timestamp, client_address)
    
    def _extract_key_info(self, data):
        """提取关键信息"""
        print("\n🔍 关键信息提取:")
        
        # 设备信息
        if 'device' in data:
            print(f"  📱 设备: {data['device']}")
        
        # 时间戳
        if 'timestamp' in data:
            print(f"  ⏰ 时间戳: {data['timestamp']}")
        
        # 传感器组
        if 'sensor_group' in data:
            print(f"  📡 传感器组: {data['sensor_group']}")
        
        # 数值指标
        numeric_metrics = {}
        for key, value in data.items():
            if isinstance(value, (int, float)):
                numeric_metrics[key] = value
        
        if numeric_metrics:
            print("  📈 数值指标:")
            for metric, value in numeric_metrics.items():
                print(f"    {metric}: {value}")
        
        # 嵌套数据
        nested_data = {}
        for key, value in data.items():
            if isinstance(value, dict):
                nested_data[key] = value
        
        if nested_data:
            print("  📦 嵌套数据:")
            for key, value in nested_data.items():
                print(f"    {key}: {len(value)} 个字段")
    
    def _process_binary_data(self, data, client_address):
        """处理二进制数据"""
        self.data_count += 1
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
        
        print(f"\n📊 [{timestamp}] 二进制数据 #{self.data_count} 来自 {client_address}")
        print(f"📏 数据长度: {len(data)} 字节")
        print(f"🔍 数据预览: {data[:100]}...")
    
    def _save_data(self, data, timestamp, client_address):
        """保存数据到文件"""
        try:
            filename = f"real_telemetry_data_{datetime.now().strftime('%Y%m%d')}.log"
            with open(filename, 'a', encoding='utf-8') as f:
                f.write(f"[{timestamp}] {client_address}: {data}\n")
        except Exception as e:
            print(f"❌ 保存数据失败: {e}")

def get_device_supported_paths():
    """获取设备支持的传感器路径"""

    print("=== 探测设备支持的传感器路径 ===\n")

    try:
        configurator = TelemetryConfigurator(
            device_config_file="config/device_config.yaml",
            telemetry_config_file="config/telemetry_config.yaml"
        )

        device_name = "nokia-alu-1"

        # 测试各种可能的传感器路径
        test_paths = [
            # 系统相关
            "/openconfig-system:system/state",
            "/openconfig-system:system/cpus",
            "/openconfig-system:system/memory",
            "/openconfig-system:system/processes",

            # 接口相关
            "/openconfig-interfaces:interfaces",
            "/openconfig-interfaces:interfaces/interface/state",
            "/openconfig-interfaces:interfaces/interface/state/counters",

            # 平台相关
            "/openconfig-platform:components",
            "/openconfig-platform:components/component/state",

            # BGP 相关
            "/openconfig-bgp:bgp",
            "/openconfig-bgp:bgp/neighbors",

            # OSPF 相关
            "/openconfig-ospfv2:ospfv2",

            # ISIS 相关
            "/openconfig-isis:isis",

            # LLDP 相关
            "/openconfig-lldp:lldp",

            # 网络实例
            "/openconfig-network-instance:network-instances",
        ]

        supported_paths = []

        print("🔍 测试传感器路径支持情况...")

        with configurator.netconf_client.get_connection(device_name) as conn:
            for path in test_paths:
                try:
                    # 尝试获取数据
                    filter_xml = f'<{path.split(":")[1].split("/")[0]} xmlns="{path.split(":")[0]}"/>'
                    result = conn.get(filter=('subtree', filter_xml))

                    if result and len(str(result)) > 100:  # 有实际数据
                        supported_paths.append(path)
                        print(f"✅ {path}")
                    else:
                        print(f"⚠️  {path} (无数据)")

                except Exception as e:
                    print(f"❌ {path} (不支持: {str(e)[:50]}...)")

        print(f"\n📊 发现 {len(supported_paths)} 个支持的路径")
        return supported_paths

    except Exception as e:
        print(f"❌ 探测失败: {e}")
        return []

def configure_comprehensive_telemetry():
    """配置全面的 telemetry 监控"""

    print("=== 配置全面的 Telemetry 监控 ===\n")

    try:
        # 初始化配置器
        configurator = TelemetryConfigurator(
            device_config_file="config/device_config.yaml",
            telemetry_config_file="config/telemetry_config.yaml"
        )

        device_name = "nokia-alu-1"
        collector_ip = "***************"
        collector_port = 57400

        print(f"1. 配置设备: {device_name}")
        print(f"   收集器: {collector_ip}:{collector_port}")

        # 测试设备连接
        print("\n2. 测试设备连接...")
        if not configurator.netconf_client.validate_connection(device_name):
            print("❌ 设备连接失败")
            return False
        print("✅ 设备连接成功")

        # 获取设备能力
        print("\n3. 获取设备能力...")
        capabilities = configurator.netconf_client.get_device_capabilities(device_name)
        print(f"✅ 设备支持 {len(capabilities)} 个能力")

        # 查找 telemetry 相关能力
        telemetry_caps = [cap for cap in capabilities if 'telemetry' in cap.lower()]
        openconfig_caps = [cap for cap in capabilities if 'openconfig' in cap.lower()]

        print(f"   Telemetry 能力: {len(telemetry_caps)} 个")
        print(f"   OpenConfig 能力: {len(openconfig_caps)} 个")

        # 显示关键能力
        print("\n   关键 OpenConfig 能力:")
        key_modules = ['system', 'interfaces', 'platform', 'bgp', 'ospf', 'isis', 'lldp']
        for module in key_modules:
            matching_caps = [cap for cap in openconfig_caps if module in cap.lower()]
            if matching_caps:
                print(f"     ✅ {module}: {len(matching_caps)} 个")
            else:
                print(f"     ❌ {module}: 不支持")

        # 探测支持的传感器路径
        print("\n4. 探测支持的传感器路径...")
        supported_paths = get_device_supported_paths()

        # 配置目标组
        print("\n5. 配置目标组...")

        # 创建目标组配置
        destination_config = f'''
<config xmlns="urn:ietf:params:xml:ns:netconf:base:1.0">
  <telemetry-system xmlns="http://openconfig.net/yang/telemetry">
    <destination-groups>
      <destination-group>
        <group-id>comprehensive-collector</group-id>
        <config>
          <group-id>comprehensive-collector</group-id>
        </config>
        <destinations>
          <destination>
            <destination-address>{collector_ip}</destination-address>
            <destination-port>{collector_port}</destination-port>
            <config>
              <destination-address>{collector_ip}</destination-address>
              <destination-port>{collector_port}</destination-port>
            </config>
          </destination>
        </destinations>
      </destination-group>
    </destination-groups>
  </telemetry-system>
</config>
'''

        # 应用配置
        with configurator.netconf_client.get_connection(device_name) as conn:
            result = conn.edit_config(target='running', config=destination_config)
            print("✅ 目标组配置成功")

        # 如果有支持的路径，尝试配置传感器组
        if supported_paths:
            print("\n6. 配置传感器组...")

            # 选择前5个支持的路径创建传感器组
            selected_paths = supported_paths[:5]

            sensor_group_config = f'''
<config xmlns="urn:ietf:params:xml:ns:netconf:base:1.0">
  <telemetry-system xmlns="http://openconfig.net/yang/telemetry">
    <sensor-groups>
      <sensor-group>
        <sensor-group-id>comprehensive-monitoring</sensor-group-id>
        <config>
          <sensor-group-id>comprehensive-monitoring</sensor-group-id>
        </config>
        <sensor-paths>
'''

            for path in selected_paths:
                sensor_group_config += f'''
          <sensor-path>
            <path>{path}</path>
            <config>
              <path>{path}</path>
            </config>
          </sensor-path>
'''

            sensor_group_config += '''
        </sensor-paths>
      </sensor-group>
    </sensor-groups>
  </telemetry-system>
</config>
'''

            try:
                with configurator.netconf_client.get_connection(device_name) as conn:
                    result = conn.edit_config(target='running', config=sensor_group_config)
                    print(f"✅ 传感器组配置成功 ({len(selected_paths)} 个路径)")
            except Exception as e:
                print(f"⚠️  传感器组配置失败: {e}")

        # 验证配置
        print("\n7. 验证配置...")
        with configurator.netconf_client.get_connection(device_name) as conn:
            filter_xml = '''
            <telemetry-system xmlns="http://openconfig.net/yang/telemetry">
              <destination-groups/>
              <sensor-groups/>
            </telemetry-system>
            '''
            result = conn.get_config(source='running', filter=('subtree', filter_xml))

            config_str = str(result)
            if 'comprehensive-collector' in config_str:
                print("✅ 目标组配置验证成功")
            else:
                print("❌ 目标组配置验证失败")

            if 'comprehensive-monitoring' in config_str:
                print("✅ 传感器组配置验证成功")
            else:
                print("⚠️  传感器组配置验证失败")

        return True

    except Exception as e:
        print(f"❌ 配置失败: {e}")
        return False

def main():
    """主函数"""
    
    # 设置详细日志
    setup_logging("DEBUG", f"real_device_debug_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    
    print("🚀 实际设备 Telemetry 调试工具")
    print("=" * 50)
    
    import argparse
    parser = argparse.ArgumentParser(description='实际设备 Telemetry 调试')
    parser.add_argument('action', choices=['configure', 'monitor', 'both'], 
                       help='操作类型: configure=配置设备, monitor=监控数据, both=配置并监控')
    parser.add_argument('--collector-ip', default='***************', 
                       help='收集器 IP 地址')
    parser.add_argument('--collector-port', type=int, default=57400, 
                       help='收集器端口')
    
    args = parser.parse_args()
    
    if args.action in ['configure', 'both']:
        print("\n📋 第一步: 配置设备 Telemetry")
        if not configure_comprehensive_telemetry():
            print("❌ 配置失败，退出")
            return 1
        print("✅ 配置完成")
    
    if args.action in ['monitor', 'both']:
        print("\n📊 第二步: 启动实时监控")
        monitor = RealTimeMonitor(args.collector_ip, args.collector_port)
        monitor.start_monitoring()
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
