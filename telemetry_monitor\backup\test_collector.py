#!/usr/bin/env python3
"""
测试收集器功能
发送模拟的 telemetry 数据到收集器
"""

import socket
import json
import time
import threading
from datetime import datetime

def send_test_data():
    """发送测试数据到收集器"""
    
    collector_ip = "***************"
    collector_port = 57400
    
    print(f"🧪 测试收集器连接: {collector_ip}:{collector_port}")
    
    try:
        # 连接到收集器
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        sock.connect((collector_ip, collector_port))
        
        print("✅ 成功连接到收集器")
        
        # 发送光传输设备测试数据
        test_messages = [
            {
                "timestamp": int(time.time() * 1000),
                "device": "nokia-alu-1",
                "sensor_group": "allSg",
                "subscription": "optical-monitoring",
                "data": {
                    "openconfig-platform:components": {
                        "component": [
                            {
                                "name": "transceiver-1/1/1",
                                "openconfig-platform-transceiver:transceiver": {
                                    "state": {
                                        "present": "PRESENT",
                                        "form-factor": "QSFP28",
                                        "connector-type": "LC",
                                        "vendor": "Nokia",
                                        "vendor-part": "3HE12345AA",
                                        "serial-no": "ABC123456789",
                                        "date-code": "210315",
                                        "fault-condition": False,
                                        "enabled": True
                                    },
                                    "physical-channels": {
                                        "channel": [
                                            {
                                                "index": 0,
                                                "state": {
                                                    "index": 0,
                                                    "description": "Lane 0",
                                                    "tx-laser": True,
                                                    "output-power": -2.5,
                                                    "input-power": -3.2,
                                                    "laser-bias-current": 45.2,
                                                    "target-output-power": -2.0
                                                }
                                            }
                                        ]
                                    }
                                }
                            }
                        ]
                    }
                }
            },
            {
                "timestamp": int(time.time() * 1000),
                "device": "nokia-alu-1",
                "sensor_group": "allSg",
                "subscription": "optical-monitoring",
                "data": {
                    "openconfig-terminal-device:terminal-device": {
                        "logical-channels": {
                            "channel": [
                                {
                                    "index": 1,
                                    "otn": {
                                        "state": {
                                            "tributary-slot-granularity": "TRIB_SLOT_1_25G",
                                            "pre-fec-ber": {
                                                "instant": 1.2e-12,
                                                "avg": 1.1e-12,
                                                "min": 9.8e-13,
                                                "max": 1.5e-12
                                            },
                                            "post-fec-ber": {
                                                "instant": 0.0,
                                                "avg": 0.0,
                                                "min": 0.0,
                                                "max": 0.0
                                            },
                                            "q-value": {
                                                "instant": 15.2,
                                                "avg": 15.1,
                                                "min": 14.8,
                                                "max": 15.5
                                            }
                                        }
                                    },
                                    "ethernet": {
                                        "state": {
                                            "in-mac-control-frames": 0,
                                            "in-mac-pause-frames": 0,
                                            "in-oversize-frames": 0,
                                            "in-undersize-frames": 0,
                                            "in-fragment-frames": 0,
                                            "in-8021q-frames": 1234567,
                                            "out-mac-control-frames": 0,
                                            "out-mac-pause-frames": 0,
                                            "out-8021q-frames": 1234560
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            },
            {
                "timestamp": int(time.time() * 1000),
                "device": "nokia-alu-1",
                "sensor_group": "allSg",
                "subscription": "optical-monitoring",
                "data": {
                    "openconfig-platform:components": {
                        "component": [
                            {
                                "name": "optical-channel-1/1/1/1",
                                "openconfig-terminal-device:optical-channel": {
                                    "state": {
                                        "frequency": 196100000,
                                        "target-output-power": 0.0,
                                        "operational-mode": 1,
                                        "line-port": "port-1/1/1"
                                    }
                                }
                            },
                            {
                                "name": "cpu-0",
                                "cpu": {
                                    "openconfig-platform-cpu:utilization": {
                                        "state": {
                                            "instant": 25.5,
                                            "avg": 24.8,
                                            "min": 20.1,
                                            "max": 35.2,
                                            "interval": 300000000
                                        }
                                    }
                                }
                            }
                        ]
                    }
                }
            }
        ]
        
        for i, message in enumerate(test_messages, 1):
            json_data = json.dumps(message) + "\n"
            sock.send(json_data.encode('utf-8'))
            print(f"📤 发送测试消息 {i}: {message['sensor_group']}")
            time.sleep(2)
        
        # 发送一些原始文本数据
        raw_messages = [
            "NOKIA-ALU-TELEMETRY: CPU=45.2% MEM=67.8% TEMP=42.5C",
            "INTERFACE-STATS: 1/1/1 IN=1234567890 OUT=987654321",
            "SYSTEM-ALERT: High temperature detected on linecard 1"
        ]
        
        for i, message in enumerate(raw_messages, 1):
            sock.send((message + "\n").encode('utf-8'))
            print(f"📤 发送原始消息 {i}: {message[:50]}...")
            time.sleep(2)
        
        sock.close()
        print("✅ 测试数据发送完成")
        
    except Exception as e:
        print(f"❌ 发送测试数据失败: {e}")

def continuous_data_sender():
    """持续发送数据"""
    
    collector_ip = "***************"
    collector_port = 57400
    
    print(f"🔄 启动持续数据发送器: {collector_ip}:{collector_port}")
    
    counter = 0
    
    while True:
        try:
            counter += 1
            
            # 连接到收集器
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            sock.connect((collector_ip, collector_port))
            
            # 生成动态光传输设备数据
            message = {
                "timestamp": int(time.time() * 1000),
                "device": "nokia-alu-1",
                "sensor_group": "allSg",
                "subscription": "optical-monitoring",
                "sequence": counter,
                "data": {
                    "openconfig-platform:components": {
                        "component": [
                            {
                                "name": f"transceiver-1/1/{(counter % 4) + 1}",
                                "openconfig-platform-transceiver:transceiver": {
                                    "state": {
                                        "present": "PRESENT",
                                        "enabled": True,
                                        "fault-condition": False,
                                        "form-factor": "QSFP28",
                                        "vendor": "Nokia"
                                    },
                                    "physical-channels": {
                                        "channel": [
                                            {
                                                "index": 0,
                                                "state": {
                                                    "index": 0,
                                                    "tx-laser": True,
                                                    "output-power": -2.0 + (counter % 10) * 0.1,
                                                    "input-power": -3.0 + (counter % 8) * 0.1,
                                                    "laser-bias-current": 40.0 + (counter % 20),
                                                    "target-output-power": -2.0
                                                }
                                            }
                                        ]
                                    }
                                }
                            },
                            {
                                "name": "cpu-0",
                                "cpu": {
                                    "openconfig-platform-cpu:utilization": {
                                        "state": {
                                            "instant": 20.0 + (counter % 30),
                                            "avg": 25.0 + (counter % 15),
                                            "min": 15.0,
                                            "max": 45.0,
                                            "interval": 300000000
                                        }
                                    }
                                }
                            }
                        ]
                    },
                    "openconfig-terminal-device:terminal-device": {
                        "logical-channels": {
                            "channel": [
                                {
                                    "index": 1,
                                    "otn": {
                                        "state": {
                                            "pre-fec-ber": {
                                                "instant": (1.0 + (counter % 5) * 0.1) * 1e-12,
                                                "avg": 1.1e-12,
                                                "min": 9.8e-13,
                                                "max": 1.5e-12
                                            },
                                            "post-fec-ber": {
                                                "instant": 0.0,
                                                "avg": 0.0
                                            },
                                            "q-value": {
                                                "instant": 15.0 + (counter % 10) * 0.1,
                                                "avg": 15.1,
                                                "min": 14.8,
                                                "max": 15.5
                                            }
                                        }
                                    },
                                    "ethernet": {
                                        "state": {
                                            "in-8021q-frames": 1000000 + counter * 1000,
                                            "out-8021q-frames": 1000000 + counter * 950,
                                            "in-mac-control-frames": counter % 100,
                                            "out-mac-control-frames": counter % 95
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
            
            json_data = json.dumps(message) + "\n"
            sock.send(json_data.encode('utf-8'))
            sock.close()
            
            # 提取 CPU 利用率用于显示
            cpu_util = "N/A"
            try:
                components = message['data']['openconfig-platform:components']['component']
                for component in components:
                    if 'cpu' in component.get('name', ''):
                        cpu_data = component.get('cpu', {}).get('openconfig-platform-cpu:utilization', {}).get('state', {})
                        cpu_util = f"{cpu_data.get('instant', 'N/A')}%"
                        break
            except:
                pass

            print(f"📊 发送连续数据 #{counter} - CPU: {cpu_util}, 收发器: transceiver-1/1/{(counter % 4) + 1}")
            
            time.sleep(10)  # 每10秒发送一次
            
        except Exception as e:
            print(f"❌ 发送连续数据失败: {e}")
            time.sleep(5)  # 失败后等待5秒重试

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='测试收集器功能')
    parser.add_argument('mode', choices=['test', 'continuous'], 
                       help='模式: test=发送测试数据, continuous=持续发送数据')
    
    args = parser.parse_args()
    
    if args.mode == 'test':
        send_test_data()
    elif args.mode == 'continuous':
        try:
            continuous_data_sender()
        except KeyboardInterrupt:
            print("\n🛑 停止持续发送")
    
    return 0

if __name__ == "__main__":
    import sys
    sys.exit(main())
