#!/usr/bin/env python3
"""
光传输设备专用 Telemetry 监控器
专门监控光传输设备的各种状态信息
"""

import socket
import json
import time
import threading
from datetime import datetime

class OpticalTelemetryMonitor:
    """光传输设备专用 Telemetry 监控器"""
    
    def __init__(self):
        self.host = '0.0.0.0'
        self.port = 57400
        self.running = False
        self.data_count = 0
        self.start_time = None
        
        # 光传输设备特定的指标统计
        self.optical_stats = {
            'transceivers': {},
            'optical_channels': {},
            'logical_channels': {'otn': {}, 'ethernet': {}},
            'cpu_utilization': {},
            'component_states': {},
            'physical_channels': {}
        }
        
    def start(self):
        """启动监控器"""
        
        print("🌟 光传输设备专用 Telemetry 监控器")
        print(f"📡 监听地址: {self.host}:{self.port}")
        print(f"📍 收集器 IP: ***************")
        print(f"⏰ 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("🔍 等待光传输设备连接...")
        print("=" * 80)
        
        try:
            # 创建服务器套接字
            server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            server_socket.bind((self.host, self.port))
            server_socket.listen(10)
            
            self.running = True
            self.start_time = datetime.now()
            
            # 启动状态报告线程
            status_thread = threading.Thread(target=self._status_reporter)
            status_thread.daemon = True
            status_thread.start()
            
            # 启动光学指标分析线程
            analysis_thread = threading.Thread(target=self._optical_analyzer)
            analysis_thread.daemon = True
            analysis_thread.start()
            
            while self.running:
                try:
                    # 接受连接
                    client_socket, client_address = server_socket.accept()
                    print(f"\n🔗 新连接: {client_address}")
                    print(f"⏰ 连接时间: {datetime.now().strftime('%H:%M:%S')}")
                    
                    # 为每个客户端启动处理线程
                    client_thread = threading.Thread(
                        target=self._handle_client,
                        args=(client_socket, client_address)
                    )
                    client_thread.daemon = True
                    client_thread.start()
                    
                except Exception as e:
                    if self.running:
                        print(f"❌ 接受连接失败: {e}")
                    
        except Exception as e:
            print(f"❌ 启动监控器失败: {e}")
        
        finally:
            try:
                server_socket.close()
            except:
                pass
    
    def _handle_client(self, client_socket, client_address):
        """处理客户端数据"""
        
        buffer = ""
        
        try:
            while self.running:
                data = client_socket.recv(8192)
                if not data:
                    break
                
                try:
                    decoded_data = data.decode('utf-8')
                    buffer += decoded_data
                    
                    # 处理完整的消息
                    while '\n' in buffer:
                        line, buffer = buffer.split('\n', 1)
                        if line.strip():
                            self._process_message(line.strip(), client_address)
                
                except UnicodeDecodeError:
                    # 处理二进制数据
                    self._process_binary_message(data, client_address)
        
        except Exception as e:
            print(f"❌ 处理客户端 {client_address} 数据失败: {e}")
        
        finally:
            try:
                client_socket.close()
                print(f"🔌 客户端 {client_address} 断开连接")
            except:
                pass
    
    def _process_message(self, data, client_address):
        """处理文本消息"""
        
        self.data_count += 1
        timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
        
        print(f"\n📊 [{timestamp}] 光传输数据 #{self.data_count} 来自 {client_address}")
        print("=" * 80)
        
        # 尝试解析 JSON
        try:
            json_data = json.loads(data)
            print("📋 光传输设备数据:")
            self._display_optical_data(json_data)
            
            # 更新光学统计
            self._update_optical_stats(json_data)
            
        except json.JSONDecodeError:
            print("📝 原始文本数据:")
            print(data)
        
        print("=" * 80)
        
        # 保存数据
        self._save_data(data, timestamp, client_address)
    
    def _display_optical_data(self, data):
        """显示光传输设备数据"""
        
        # 基本信息
        if 'device' in data:
            print(f"📱 设备: {data['device']}")
        
        if 'timestamp' in data:
            print(f"⏰ 设备时间戳: {data['timestamp']}")
        
        if 'sensor_group' in data:
            print(f"📡 传感器组: {data['sensor_group']}")
        
        # 检查是否包含光传输相关数据
        optical_data = self._extract_optical_data(data)
        
        if optical_data['transceivers']:
            print("\n🔌 光收发器状态:")
            for transceiver_id, info in optical_data['transceivers'].items():
                print(f"   {transceiver_id}:")
                for key, value in info.items():
                    if isinstance(value, (int, float)):
                        if 'power' in key.lower():
                            print(f"     {key}: {value:.2f} dBm")
                        elif 'temperature' in key.lower():
                            print(f"     {key}: {value:.1f} °C")
                        else:
                            print(f"     {key}: {value}")
                    else:
                        print(f"     {key}: {value}")
        
        if optical_data['optical_channels']:
            print("\n🌈 光通道状态:")
            for channel_id, info in optical_data['optical_channels'].items():
                print(f"   通道 {channel_id}:")
                for key, value in info.items():
                    if isinstance(value, (int, float)):
                        if 'frequency' in key.lower():
                            print(f"     {key}: {value:.2f} GHz")
                        elif 'power' in key.lower():
                            print(f"     {key}: {value:.2f} dBm")
                        else:
                            print(f"     {key}: {value}")
                    else:
                        print(f"     {key}: {value}")
        
        if optical_data['logical_channels']:
            print("\n📊 逻辑通道状态:")
            for channel_type, channels in optical_data['logical_channels'].items():
                print(f"   {channel_type.upper()} 通道:")
                for channel_id, info in channels.items():
                    print(f"     通道 {channel_id}:")
                    for key, value in list(info.items())[:5]:  # 显示前5个指标
                        if isinstance(value, (int, float)):
                            print(f"       {key}: {value}")
                        else:
                            print(f"       {key}: {value}")
                    if len(info) > 5:
                        print(f"       ... 还有 {len(info) - 5} 个指标")
        
        if optical_data['cpu_utilization']:
            print("\n💻 CPU 利用率:")
            for cpu_id, utilization in optical_data['cpu_utilization'].items():
                print(f"   CPU {cpu_id}: {utilization:.1f}%")
        
        if optical_data['component_states']:
            print("\n🔧 组件状态:")
            for component_id, state in optical_data['component_states'].items():
                print(f"   {component_id}: {state}")
        
        # 显示完整 JSON（如果数据较小）
        json_str = json.dumps(data, indent=2, ensure_ascii=False)
        if len(json_str) < 1000:
            print(f"\n📄 完整数据:")
            print(json_str)
        else:
            print(f"\n📄 数据大小: {len(json_str)} 字符 (太大，不完整显示)")
    
    def _extract_optical_data(self, data):
        """提取光传输相关数据"""
        
        optical_data = {
            'transceivers': {},
            'optical_channels': {},
            'logical_channels': {'otn': {}, 'ethernet': {}},
            'cpu_utilization': {},
            'component_states': {},
            'physical_channels': {}
        }
        
        # 递归搜索光传输相关数据
        def search_optical_data(obj, path=""):
            if isinstance(obj, dict):
                for key, value in obj.items():
                    current_path = f"{path}/{key}" if path else key
                    
                    # 检查是否是光收发器数据
                    if 'transceiver' in key.lower():
                        optical_data['transceivers'][current_path] = value
                    
                    # 检查是否是光通道数据
                    elif 'optical-channel' in key.lower():
                        optical_data['optical_channels'][current_path] = value
                    
                    # 检查是否是逻辑通道数据
                    elif 'otn' in key.lower():
                        optical_data['logical_channels']['otn'][current_path] = value
                    elif 'ethernet' in key.lower():
                        optical_data['logical_channels']['ethernet'][current_path] = value
                    
                    # 检查是否是 CPU 利用率
                    elif 'cpu' in key.lower() and 'utilization' in key.lower():
                        optical_data['cpu_utilization'][current_path] = value
                    
                    # 检查是否是组件状态
                    elif 'component' in key.lower() and 'state' in key.lower():
                        optical_data['component_states'][current_path] = value
                    
                    # 检查是否是物理通道
                    elif 'physical-channel' in key.lower():
                        optical_data['physical_channels'][current_path] = value
                    
                    # 递归搜索
                    if isinstance(value, (dict, list)):
                        search_optical_data(value, current_path)
            
            elif isinstance(obj, list):
                for i, item in enumerate(obj):
                    search_optical_data(item, f"{path}[{i}]")
        
        search_optical_data(data)
        return optical_data
    
    def _update_optical_stats(self, data):
        """更新光学统计信息"""
        
        optical_data = self._extract_optical_data(data)
        
        # 更新统计信息
        for category, items in optical_data.items():
            if category not in self.optical_stats:
                self.optical_stats[category] = {}
            
            if isinstance(items, dict):
                for item_id, item_data in items.items():
                    if item_id not in self.optical_stats[category]:
                        self.optical_stats[category][item_id] = []
                    
                    self.optical_stats[category][item_id].append({
                        'timestamp': datetime.now(),
                        'data': item_data
                    })
                    
                    # 只保留最近100个数据点
                    if len(self.optical_stats[category][item_id]) > 100:
                        self.optical_stats[category][item_id] = self.optical_stats[category][item_id][-100:]
    
    def _optical_analyzer(self):
        """光学指标分析线程"""
        while self.running:
            time.sleep(60)  # 每分钟分析一次
            if self.running:
                self._analyze_optical_trends()
    
    def _analyze_optical_trends(self):
        """分析光学趋势"""
        if not any(self.optical_stats.values()):
            return
        
        print(f"\n🔍 === 光学指标趋势分析 ===")
        
        # 分析收发器趋势
        if self.optical_stats['transceivers']:
            print("📡 收发器趋势:")
            for transceiver_id, history in self.optical_stats['transceivers'].items():
                if len(history) >= 2:
                    latest = history[-1]['data']
                    previous = history[-2]['data']
                    print(f"   {transceiver_id}: 最新数据点 vs 上一个数据点")
        
        # 分析 CPU 利用率趋势
        if self.optical_stats['cpu_utilization']:
            print("💻 CPU 利用率趋势:")
            for cpu_id, history in self.optical_stats['cpu_utilization'].items():
                if len(history) >= 5:
                    recent_values = [item['data'] for item in history[-5:] if isinstance(item['data'], (int, float))]
                    if recent_values:
                        avg_cpu = sum(recent_values) / len(recent_values)
                        print(f"   {cpu_id}: 最近5次平均 {avg_cpu:.1f}%")
        
        print("-" * 40)
    
    def _process_binary_message(self, data, client_address):
        """处理二进制消息"""
        
        self.data_count += 1
        timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
        
        print(f"\n📊 [{timestamp}] 二进制数据 #{self.data_count} 来自 {client_address}")
        print("=" * 80)
        print(f"📏 数据长度: {len(data)} 字节")
        print(f"🔍 数据预览: {data[:100]}...")
        print("=" * 80)
    
    def _save_data(self, data, timestamp, client_address):
        """保存数据到文件"""
        try:
            filename = f"optical_telemetry_{datetime.now().strftime('%Y%m%d')}.log"
            with open(filename, 'a', encoding='utf-8') as f:
                f.write(f"[{timestamp}] {client_address}: {data}\n")
        except Exception as e:
            print(f"❌ 保存数据失败: {e}")
    
    def _status_reporter(self):
        """状态报告线程"""
        while self.running:
            time.sleep(30)  # 每30秒报告一次
            if self.running and self.start_time:
                runtime = datetime.now() - self.start_time
                print(f"\n📊 光传输设备状态报告 - 运行时间: {runtime}, 已接收: {self.data_count} 条消息")
                
                # 报告光学统计摘要
                total_transceivers = len(self.optical_stats.get('transceivers', {}))
                total_channels = len(self.optical_stats.get('optical_channels', {}))

                # 安全地获取逻辑通道数量
                logical_channels = self.optical_stats.get('logical_channels', {})
                otn_channels = len(logical_channels.get('otn', {}))
                ethernet_channels = len(logical_channels.get('ethernet', {}))
                total_logical_channels = otn_channels + ethernet_channels

                print(f"   📡 收发器: {total_transceivers} 个")
                print(f"   🌈 光通道: {total_channels} 个")
                print(f"   📊 逻辑通道: {total_logical_channels} 个 (OTN: {otn_channels}, 以太网: {ethernet_channels})")
    
    def stop(self):
        """停止监控器"""
        self.running = False
        print("🛑 光传输设备监控器已停止")

def main():
    """主函数"""
    
    monitor = OpticalTelemetryMonitor()
    
    try:
        monitor.start()
    except KeyboardInterrupt:
        print("\n收到停止信号...")
        monitor.stop()
    except Exception as e:
        print(f"监控器运行失败: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    import sys
    sys.exit(main())
