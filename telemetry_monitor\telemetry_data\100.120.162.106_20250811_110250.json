{"message_number": 1, "timestamp": "2025-08-11 11:02:50.902000", "raw_timestamp_ns": 1754881370902000000, "device_ip": "***************", "message_type": "update", "prefix": "/openconfig-platform:components", "alias": "", "atomic": false, "updates": [{"path": "/component[name=CHASSIS-1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C5]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C3]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C3]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-33]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-7-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C3]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-5]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-11]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-8-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-12]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=PSU-1-22]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C3]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "state"}]}}}, {"path": "/component[name=PSU-1-21]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-7]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-11]/cpu/openconfig-platform-cpu:utilization", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-8]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-1-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-34]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-32]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-31]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-5-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-12]/cpu/openconfig-platform-cpu:utilization", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C5]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}], "deletes": [], "raw_notification": {"timestamp": "1754881370902000000", "prefix": {"elem": [{"name": "openconfig-platform:components"}]}, "update": [{"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}]}}
{"message_number": 2, "timestamp": "2025-08-11 11:02:51.902000", "raw_timestamp_ns": 1754881371902000000, "device_ip": "***************", "message_type": "update", "prefix": "/openconfig-platform:components", "alias": "", "atomic": false, "updates": [{"path": "/component[name=CHASSIS-1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C5]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C3]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C3]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-33]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-7-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C3]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-5]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-11]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-8-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-12]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=PSU-1-22]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C3]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "state"}]}}}, {"path": "/component[name=PSU-1-21]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-7]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-11]/cpu/openconfig-platform-cpu:utilization", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-8]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-1-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-34]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-32]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-31]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-5-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-12]/cpu/openconfig-platform-cpu:utilization", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C5]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}], "deletes": [], "raw_notification": {"timestamp": "1754881371902000000", "prefix": {"elem": [{"name": "openconfig-platform:components"}]}, "update": [{"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}]}}
{"message_number": 3, "timestamp": "2025-08-11 11:02:52.902000", "raw_timestamp_ns": 1754881372902000000, "device_ip": "***************", "message_type": "update", "prefix": "/openconfig-platform:components", "alias": "", "atomic": false, "updates": [{"path": "/component[name=CHASSIS-1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C5]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C3]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C3]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-33]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-7-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C3]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-5]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-11]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-8-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-12]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=PSU-1-22]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C3]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "state"}]}}}, {"path": "/component[name=PSU-1-21]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-7]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-11]/cpu/openconfig-platform-cpu:utilization", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-8]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-1-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-34]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-32]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-31]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-5-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-12]/cpu/openconfig-platform-cpu:utilization", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C5]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}], "deletes": [], "raw_notification": {"timestamp": "1754881372902000000", "prefix": {"elem": [{"name": "openconfig-platform:components"}]}, "update": [{"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}]}}
{"message_number": 4, "timestamp": "2025-08-11 11:02:53.902000", "raw_timestamp_ns": 1754881373902000000, "device_ip": "***************", "message_type": "update", "prefix": "/openconfig-platform:components", "alias": "", "atomic": false, "updates": [{"path": "/component[name=CHASSIS-1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C5]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C3]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C3]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-33]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-7-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C3]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-5]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-11]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-8-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-12]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=PSU-1-22]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C3]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "state"}]}}}, {"path": "/component[name=PSU-1-21]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-7]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-11]/cpu/openconfig-platform-cpu:utilization", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-8]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-1-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-34]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-32]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-31]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-5-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-12]/cpu/openconfig-platform-cpu:utilization", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C5]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}], "deletes": [], "raw_notification": {"timestamp": "1754881373902000000", "prefix": {"elem": [{"name": "openconfig-platform:components"}]}, "update": [{"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}]}}
{"message_number": 5, "timestamp": "2025-08-11 11:02:54.902000", "raw_timestamp_ns": 1754881374902000000, "device_ip": "***************", "message_type": "update", "prefix": "/openconfig-platform:components", "alias": "", "atomic": false, "updates": [{"path": "/component[name=CHASSIS-1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C5]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C3]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C3]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-33]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-7-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C3]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-5]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-11]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-8-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-12]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=PSU-1-22]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C3]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "state"}]}}}, {"path": "/component[name=PSU-1-21]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-7]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-11]/cpu/openconfig-platform-cpu:utilization", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-8]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-1-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-34]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-32]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-31]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-5-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-12]/cpu/openconfig-platform-cpu:utilization", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C5]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}], "deletes": [], "raw_notification": {"timestamp": "1754881374902000000", "prefix": {"elem": [{"name": "openconfig-platform:components"}]}, "update": [{"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}]}}
{"message_number": 6, "timestamp": "2025-08-11 11:02:55.902000", "raw_timestamp_ns": 1754881375902000000, "device_ip": "***************", "message_type": "update", "prefix": "/openconfig-platform:components", "alias": "", "atomic": false, "updates": [{"path": "/component[name=CHASSIS-1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C5]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C3]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C3]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-33]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-7-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C3]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-5]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-11]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-8-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-12]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=PSU-1-22]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C3]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "state"}]}}}, {"path": "/component[name=PSU-1-21]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-7]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-11]/cpu/openconfig-platform-cpu:utilization", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-8]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-1-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-34]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-32]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-31]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-5-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-12]/cpu/openconfig-platform-cpu:utilization", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C5]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}], "deletes": [], "raw_notification": {"timestamp": "1754881375902000000", "prefix": {"elem": [{"name": "openconfig-platform:components"}]}, "update": [{"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}]}}
{"message_number": 7, "timestamp": "2025-08-11 11:02:56.902000", "raw_timestamp_ns": 1754881376902000000, "device_ip": "***************", "message_type": "update", "prefix": "/openconfig-platform:components", "alias": "", "atomic": false, "updates": [{"path": "/component[name=CHASSIS-1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C5]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C3]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C3]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-33]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-7-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C3]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-5]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-11]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-8-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-12]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=PSU-1-22]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C3]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "state"}]}}}, {"path": "/component[name=PSU-1-21]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-7]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-11]/cpu/openconfig-platform-cpu:utilization", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-8]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-1-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-34]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-32]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-31]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-5-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-12]/cpu/openconfig-platform-cpu:utilization", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C5]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}], "deletes": [], "raw_notification": {"timestamp": "1754881376902000000", "prefix": {"elem": [{"name": "openconfig-platform:components"}]}, "update": [{"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}]}}
{"message_number": 8, "timestamp": "2025-08-11 11:02:57.902000", "raw_timestamp_ns": 1754881377902000000, "device_ip": "***************", "message_type": "update", "prefix": "/openconfig-platform:components", "alias": "", "atomic": false, "updates": [{"path": "/component[name=CHASSIS-1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C5]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C3]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C3]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-33]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-7-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C3]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-5]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-11]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-8-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-12]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=PSU-1-22]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C3]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "state"}]}}}, {"path": "/component[name=PSU-1-21]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-7]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-11]/cpu/openconfig-platform-cpu:utilization", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-8]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-1-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-34]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-32]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-31]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-5-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-12]/cpu/openconfig-platform-cpu:utilization", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C5]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}], "deletes": [], "raw_notification": {"timestamp": "1754881377902000000", "prefix": {"elem": [{"name": "openconfig-platform:components"}]}, "update": [{"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}]}}
{"message_number": 9, "timestamp": "2025-08-11 11:02:58.902000", "raw_timestamp_ns": 1754881378902000000, "device_ip": "***************", "message_type": "update", "prefix": "/openconfig-platform:components", "alias": "", "atomic": false, "updates": [{"path": "/component[name=CHASSIS-1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C5]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C3]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C3]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-33]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-7-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C3]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-5]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-11]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-8-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-12]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=PSU-1-22]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C3]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "state"}]}}}, {"path": "/component[name=PSU-1-21]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-7]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-11]/cpu/openconfig-platform-cpu:utilization", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-8]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-1-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-34]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-32]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-31]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-5-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-12]/cpu/openconfig-platform-cpu:utilization", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C5]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}], "deletes": [], "raw_notification": {"timestamp": "1754881378902000000", "prefix": {"elem": [{"name": "openconfig-platform:components"}]}, "update": [{"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}]}}
{"message_number": 10, "timestamp": "2025-08-11 11:02:59.902000", "raw_timestamp_ns": 1754881379902000000, "device_ip": "***************", "message_type": "update", "prefix": "/openconfig-platform:components", "alias": "", "atomic": false, "updates": [{"path": "/component[name=CHASSIS-1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C5]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C3]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C3]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-33]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-7-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C3]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-5]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-11]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-8-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-12]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=PSU-1-22]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C3]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "state"}]}}}, {"path": "/component[name=PSU-1-21]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-7]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-11]/cpu/openconfig-platform-cpu:utilization", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-8]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-1-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-34]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-32]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-31]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-5-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-12]/cpu/openconfig-platform-cpu:utilization", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C5]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}], "deletes": [], "raw_notification": {"timestamp": "1754881379902000000", "prefix": {"elem": [{"name": "openconfig-platform:components"}]}, "update": [{"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}]}}
{"message_number": 11, "timestamp": "2025-08-11 11:03:00.903000", "raw_timestamp_ns": 1754881380903000000, "device_ip": "***************", "message_type": "update", "prefix": "/openconfig-platform:components", "alias": "", "atomic": false, "updates": [{"path": "/component[name=CHASSIS-1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C5]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C3]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C3]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-33]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-7-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C3]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-5]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-11]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-8-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-12]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=PSU-1-22]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C3]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "state"}]}}}, {"path": "/component[name=PSU-1-21]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-7]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-11]/cpu/openconfig-platform-cpu:utilization", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-8]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-1-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-34]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-32]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-31]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-5-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-12]/cpu/openconfig-platform-cpu:utilization", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C5]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}], "deletes": [], "raw_notification": {"timestamp": "1754881380903000000", "prefix": {"elem": [{"name": "openconfig-platform:components"}]}, "update": [{"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}]}}
{"message_number": 12, "timestamp": "2025-08-11 11:03:01.902000", "raw_timestamp_ns": 1754881381902000000, "device_ip": "***************", "message_type": "update", "prefix": "/openconfig-platform:components", "alias": "", "atomic": false, "updates": [{"path": "/component[name=CHASSIS-1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C5]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C3]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C3]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-33]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-7-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C3]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-5]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-11]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-8-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-12]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=PSU-1-22]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C3]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "state"}]}}}, {"path": "/component[name=PSU-1-21]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-7]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-11]/cpu/openconfig-platform-cpu:utilization", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-8]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-1-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-34]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-32]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-31]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-5-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-12]/cpu/openconfig-platform-cpu:utilization", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C5]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}], "deletes": [], "raw_notification": {"timestamp": "1754881381902000000", "prefix": {"elem": [{"name": "openconfig-platform:components"}]}, "update": [{"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}]}}
{"message_number": 13, "timestamp": "2025-08-11 11:03:02.902000", "raw_timestamp_ns": 1754881382902000000, "device_ip": "***************", "message_type": "update", "prefix": "/openconfig-platform:components", "alias": "", "atomic": false, "updates": [{"path": "/component[name=CHASSIS-1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C5]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C3]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C3]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-33]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-7-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C3]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-5]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-11]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-8-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-12]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=PSU-1-22]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C3]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "state"}]}}}, {"path": "/component[name=PSU-1-21]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-7]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-11]/cpu/openconfig-platform-cpu:utilization", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-8]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-1-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-34]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-32]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-31]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-5-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-12]/cpu/openconfig-platform-cpu:utilization", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C5]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}], "deletes": [], "raw_notification": {"timestamp": "1754881382902000000", "prefix": {"elem": [{"name": "openconfig-platform:components"}]}, "update": [{"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}]}}
{"message_number": 14, "timestamp": "2025-08-11 11:03:03.902000", "raw_timestamp_ns": 1754881383902000000, "device_ip": "***************", "message_type": "update", "prefix": "/openconfig-platform:components", "alias": "", "atomic": false, "updates": [{"path": "/component[name=CHASSIS-1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C5]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C3]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C3]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-33]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-7-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C3]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-5]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-11]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-8-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-12]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=PSU-1-22]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C3]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "state"}]}}}, {"path": "/component[name=PSU-1-21]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-7]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-11]/cpu/openconfig-platform-cpu:utilization", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-8]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-1-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-34]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-32]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-31]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-5-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-12]/cpu/openconfig-platform-cpu:utilization", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C5]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}], "deletes": [], "raw_notification": {"timestamp": "1754881383902000000", "prefix": {"elem": [{"name": "openconfig-platform:components"}]}, "update": [{"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}]}}
