import grpc
from concurrent import futures
import json
import logging
from datetime import datetime
import os

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class TelemetryServer(object):
    def __init__(self, server_address, server_port, output_dir):
        self.server_address = server_address
        self.server_port = server_port
        self.output_dir = output_dir
        self.server = grpc.server(futures.ThreadPoolExecutor(max_workers=10))
        
    def serve(self):
        try:
            self.server.add_insecure_port(f'{self.server_address}:{self.server_port}')
            self.server.start()
            logger.info(f'gRPC server started on {self.server_address}:{self.server_port}')
            self.server.wait_for_termination()
        except Exception as e:
            logger.error(f'Error starting server: {e}')
            raise

    def process_telemetry_data(self, request):
        """Process incoming telemetry data and write to file"""
        device_id = request.node_id
        timestamp = datetime.utcnow().isoformat()
        
        data = {
            'timestamp': timestamp,
            'device': device_id,
            'subscription_id': request.subscription_id,
            'sensor_path': request.sensor_path,
            'data': request.data  # You might need to decode this based on your data format
        }
        
        output_file = os.path.join(self.output_dir, f'device_{device_id}.json')
        with open(output_file, 'a') as f:
            json.dump(data, f)
            f.write('\n')

def main():
    # Create output directory
    output_dir = "telemetry_data"
    os.makedirs(output_dir, exist_ok=True)
    
    # Start server
    server = TelemetryServer('***************', 50051, output_dir)
    try:
        server.serve()
    except KeyboardInterrupt:
        logger.info("Shutting down server...")
        server.server.stop(0)

if __name__ == '__main__':
    main()
