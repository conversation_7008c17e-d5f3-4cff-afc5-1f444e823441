import grpc
import json
import threading
import logging
import signal
import os
import time
from datetime import datetime
from queue import Queue
from contextlib import contextmanager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Global flag for graceful shutdown
running = True

# Signal handler for graceful shutdown
def signal_handler(signum, frame):
    global running
    logger.info("Received shutdown signal, stopping threads...")
    running = False

signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)

# Buffer for batch writing
class TelemetryBuffer:
    def __init__(self, filename, max_buffer_size=100):
        self.filename = filename
        self.buffer = []
        self.max_buffer_size = max_buffer_size
        self.lock = threading.Lock()
        
    def add(self, data):
        with self.lock:
            self.buffer.append(data)
            if len(self.buffer) >= self.max_buffer_size:
                self.flush()
                
    def flush(self):
        with self.lock:
            if self.buffer:
                with open(self.filename, "a") as f:
                    for data in self.buffer:
                        f.write(json.dumps(data) + "\n")
                self.buffer.clear()

@contextmanager
def grpc_channel_manager(address, port, timeout=5):
    """Context manager for gRPC channel with reconnection logic"""
    channel = None
    try:
        channel = grpc.insecure_channel(f"{address}:{port}")
        yield channel
    except Exception as e:
        logger.error(f"Channel error: {e}")
        raise
    finally:
        if channel:
            channel.close()

# Define a function to handle telemetry data for a single device
def monitor_device(device_address, device_port, output_file):
    """
    Connects to the device's telemetry stream and writes data to a JSON file.

    :param device_address: IP address of the device
    :param device_port: Port for the telemetry stream
    :param output_file: Path to the output JSON file
    """
    logger.info(f"Starting monitoring for device {device_address}")
    buffer = TelemetryBuffer(output_file)
    retry_count = 0
    max_retries = 3

    while running and retry_count < max_retries:
        try:
            with grpc_channel_manager(device_address, device_port) as channel:
                # Here you would create your gRPC stub and subscription
                # For example:
                # stub = your_proto_pb2_grpc.TelemetryStub(channel)
                # subscription_request = your_proto_pb2.SubscriptionRequest()
                
                while running:
                    try:
                        # In real implementation, replace this with actual gRPC call
                        telemetry_data = {
                            "timestamp": datetime.utcnow().isoformat(),
                            "device": device_address,
                            "data": {
                                "example_metric": 123.45
                            }
                        }
                        
                        buffer.add(telemetry_data)
                        
                    except grpc.RpcError as rpc_error:
                        logger.error(f"RPC Error for device {device_address}: {rpc_error}")
                        break
                        
        except Exception as e:
            retry_count += 1
            logger.error(f"Error monitoring device {device_address} (attempt {retry_count}/{max_retries}): {e}")
            if retry_count < max_retries:
                logger.info(f"Retrying connection to {device_address} in 5 seconds...")
                time.sleep(5)
                
    # Final flush of any remaining data
    buffer.flush()
    logger.info(f"Stopped monitoring device {device_address}")

    except Exception as e:
        print(f"Error monitoring device {device_address}: {e}")

# List of devices to monitor (replace with actual device details)
devices = [
    {"address": "***************", "port": 50051, "output_file": "device_***************.json"},
    # Add more devices as needed
]

# Create and start a thread for each device
threads = []
for device in devices:
    t = threading.Thread(
        target=monitor_device,
        args=(device["address"], device["port"], device["output_file"])
    )
    threads.append(t)
    t.start()

# Wait for all threads to complete
try:
    for t in threads:
        t.join()
except KeyboardInterrupt:
    logger.info("Received keyboard interrupt, initiating shutdown...")
    running = False
    # Wait again for threads to finish
    for t in threads:
        t.join()

logger.info("All monitoring threads have stopped. Exiting...")
