#!/usr/bin/env python3
"""
YANG 模板测试
"""

import unittest
import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.yang_templates import YangTemplates


class TestYangTemplates(unittest.TestCase):
    """YANG 模板测试类"""
    
    def setUp(self):
        """测试初始化"""
        self.yang_templates = YangTemplates()
    
    def test_init(self):
        """测试初始化"""
        self.assertIsInstance(self.yang_templates.sensor_paths, dict)
        self.assertIsInstance(self.yang_templates.config_templates, dict)
        self.assertIn('telemetry', self.yang_templates.NAMESPACES)
    
    def test_get_sensor_paths(self):
        """测试获取传感器路径"""
        # 测试有效的类别和指标
        paths = self.yang_templates.get_sensor_paths('system', 'cpu_utilization')
        self.assertIsInstance(paths, list)
        self.assertGreater(len(paths), 0)
        
        # 测试路径格式
        for path in paths:
            self.assertTrue(path.startswith('/openconfig-'))
    
    def test_get_sensor_paths_invalid_category(self):
        """测试无效类别"""
        with self.assertRaises(ValueError):
            self.yang_templates.get_sensor_paths('invalid_category', 'cpu_utilization')
    
    def test_get_sensor_paths_invalid_metric(self):
        """测试无效指标"""
        with self.assertRaises(ValueError):
            self.yang_templates.get_sensor_paths('system', 'invalid_metric')
    
    def test_get_all_paths_for_category(self):
        """测试获取类别的所有路径"""
        paths = self.yang_templates.get_all_paths_for_category('system')
        self.assertIsInstance(paths, dict)
        self.assertIn('cpu_utilization', paths)
        self.assertIn('memory_utilization', paths)
    
    def test_generate_sensor_group_xml(self):
        """测试生成传感器组 XML"""
        paths = [
            '/openconfig-system:system/cpus/cpu/state/total',
            '/openconfig-system:system/memory/state/physical'
        ]
        
        xml = self.yang_templates.generate_sensor_group_xml('test-group', paths)
        
        self.assertIn('test-group', xml)
        self.assertIn('sensor-group-id', xml)
        self.assertIn('sensor-paths', xml)
        for path in paths:
            self.assertIn(path, xml)
    
    def test_generate_destination_group_xml(self):
        """测试生成目标组 XML"""
        destinations = [
            {
                'address': '*************',
                'port': 57400,
                'protocol': 'grpc',
                'encoding': 'json'
            }
        ]
        
        xml = self.yang_templates.generate_destination_group_xml('test-dest', destinations)
        
        self.assertIn('test-dest', xml)
        self.assertIn('destination-group', xml)
        self.assertIn('*************', xml)
        self.assertIn('57400', xml)
        self.assertIn('grpc', xml)
        self.assertIn('json', xml)
    
    def test_generate_subscription_xml(self):
        """测试生成订阅 XML"""
        config = {
            'subscription_id': 'test-sub',
            'sensor_group': 'test-sensor',
            'destination_group': 'test-dest',
            'sample_interval': 30000,
            'source_address': '***********',
            'qos_marking': 0,
            'suppress_redundant': 'false',
            'heartbeat_interval': 60000
        }
        
        xml = self.yang_templates.generate_subscription_xml(config)
        
        self.assertIn('test-sub', xml)
        self.assertIn('test-sensor', xml)
        self.assertIn('test-dest', xml)
        self.assertIn('30000', xml)
    
    def test_generate_complete_telemetry_config(self):
        """测试生成完整配置"""
        sensor_groups = [{
            'group_id': 'test-sensor',
            'paths': ['/openconfig-system:system/cpus/cpu/state/total']
        }]
        
        destination_groups = [{
            'group_id': 'test-dest',
            'destinations': [{
                'address': '*************',
                'port': 57400,
                'protocol': 'grpc',
                'encoding': 'json'
            }]
        }]
        
        subscriptions = [{
            'subscription_id': 'test-sub',
            'sensor_group': 'test-sensor',
            'destination_group': 'test-dest',
            'sample_interval': 30000,
            'source_address': '',
            'qos_marking': 0,
            'suppress_redundant': 'false',
            'heartbeat_interval': 60000
        }]
        
        xml = self.yang_templates.generate_complete_telemetry_config(
            sensor_groups, destination_groups, subscriptions
        )
        
        self.assertIn('<config xmlns="urn:ietf:params:xml:ns:netconf:base:1.0">', xml)
        self.assertIn('telemetry-system', xml)
        self.assertIn('sensor-groups', xml)
        self.assertIn('destination-groups', xml)
        self.assertIn('subscriptions', xml)
    
    def test_list_available_categories(self):
        """测试列出可用类别"""
        categories = self.yang_templates.list_available_categories()
        self.assertIsInstance(categories, list)
        self.assertIn('system', categories)
        self.assertIn('interface', categories)
        self.assertIn('routing', categories)
    
    def test_list_available_metrics(self):
        """测试列出可用指标"""
        metrics = self.yang_templates.list_available_metrics('system')
        self.assertIsInstance(metrics, list)
        self.assertIn('cpu_utilization', metrics)
        self.assertIn('memory_utilization', metrics)
    
    def test_validate_paths(self):
        """测试路径验证"""
        valid_paths = [
            '/openconfig-system:system/cpus/cpu/state/total',
            '/openconfig-interfaces:interfaces/interface/state/counters/in-octets'
        ]
        invalid_paths = [
            '/invalid/path',
            '/another/invalid/path'
        ]
        
        all_paths = valid_paths + invalid_paths
        results = self.yang_templates.validate_paths(all_paths)
        
        for path in valid_paths:
            self.assertTrue(results[path], f"路径 {path} 应该是有效的")
        
        for path in invalid_paths:
            self.assertFalse(results[path], f"路径 {path} 应该是无效的")


if __name__ == '__main__':
    unittest.main()