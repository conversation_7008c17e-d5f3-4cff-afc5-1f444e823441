#!/usr/bin/env python3
"""
增强的 Telemetry 收集器
专门用于实际设备调试，提供实时监控和详细分析
"""

import socket
import json
import threading
import time
import os
from datetime import datetime
import logging
from collections import defaultdict, deque

class EnhancedTelemetryCollector:
    """增强的 Telemetry 收集器"""
    
    def __init__(self, host='***************', port=57400):
        """
        初始化收集器
        
        Args:
            host: 监听地址
            port: 监听端口
        """
        self.host = host
        self.port = port
        self.running = False
        self.server_socket = None
        self.clients = []
        self.data_count = 0
        self.start_time = None
        
        # 数据统计
        self.stats = {
            'total_messages': 0,
            'json_messages': 0,
            'binary_messages': 0,
            'error_messages': 0,
            'devices': set(),
            'sensor_groups': set(),
            'metrics': defaultdict(list),
            'message_rates': deque(maxlen=60)  # 最近60秒的消息速率
        }
        
        # 实时数据缓存
        self.recent_data = deque(maxlen=100)  # 最近100条消息
        
        # 设置日志
        self._setup_logging()
    
    def _setup_logging(self):
        """设置日志"""
        log_format = '%(asctime)s - %(levelname)s - %(message)s'
        logging.basicConfig(
            level=logging.INFO,
            format=log_format,
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler(f'enhanced_collector_{datetime.now().strftime("%Y%m%d")}.log', encoding='utf-8')
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def start(self):
        """启动收集器"""
        try:
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.server_socket.bind((self.host, self.port))
            self.server_socket.listen(10)
            
            self.running = True
            self.start_time = datetime.now()
            
            print(f"🚀 增强 Telemetry 收集器启动")
            print(f"📡 监听地址: {self.host}:{self.port}")
            print(f"⏰ 启动时间: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
            print("=" * 60)
            
            # 启动各种线程
            self._start_threads()
            
            # 主循环 - 接受连接
            self._accept_connections()
            
        except Exception as e:
            self.logger.error(f"启动收集器失败: {e}")
            return False
    
    def _start_threads(self):
        """启动各种后台线程"""
        
        # 统计报告线程
        stats_thread = threading.Thread(target=self._stats_reporter)
        stats_thread.daemon = True
        stats_thread.start()
        
        # 实时显示线程
        display_thread = threading.Thread(target=self._real_time_display)
        display_thread.daemon = True
        display_thread.start()
        
        # 数据分析线程
        analysis_thread = threading.Thread(target=self._data_analyzer)
        analysis_thread.daemon = True
        analysis_thread.start()
    
    def _accept_connections(self):
        """接受客户端连接"""
        while self.running:
            try:
                client_socket, client_address = self.server_socket.accept()
                
                print(f"\n🔗 新连接: {client_address}")
                self.logger.info(f"新连接来自: {client_address}")
                
                self.clients.append(client_socket)
                
                # 为每个客户端启动处理线程
                client_thread = threading.Thread(
                    target=self._handle_client,
                    args=(client_socket, client_address)
                )
                client_thread.daemon = True
                client_thread.start()
                
            except Exception as e:
                if self.running:
                    self.logger.error(f"接受连接失败: {e}")
                break
    
    def _handle_client(self, client_socket, client_address):
        """处理客户端数据"""
        buffer = ""
        
        try:
            while self.running:
                data = client_socket.recv(8192)  # 增大缓冲区
                if not data:
                    break
                
                try:
                    decoded_data = data.decode('utf-8')
                    buffer += decoded_data
                    
                    # 处理完整的消息
                    while '\n' in buffer:
                        line, buffer = buffer.split('\n', 1)
                        if line.strip():
                            self._process_message(line.strip(), client_address, 'text')
                            
                except UnicodeDecodeError:
                    # 处理二进制数据
                    self._process_message(data, client_address, 'binary')
                
        except Exception as e:
            self.logger.error(f"处理客户端 {client_address} 数据失败: {e}")
        
        finally:
            try:
                client_socket.close()
                if client_socket in self.clients:
                    self.clients.remove(client_socket)
                print(f"🔌 客户端 {client_address} 断开连接")
                self.logger.info(f"客户端 {client_address} 断开连接")
            except:
                pass
    
    def _process_message(self, data, client_address, data_type):
        """处理消息"""
        self.data_count += 1
        self.stats['total_messages'] += 1
        timestamp = datetime.now()
        
        message_info = {
            'id': self.data_count,
            'timestamp': timestamp,
            'client': client_address,
            'type': data_type,
            'data': data,
            'parsed_data': None,
            'size': len(str(data))
        }
        
        if data_type == 'text':
            # 尝试解析 JSON
            try:
                parsed_data = json.loads(data)
                message_info['parsed_data'] = parsed_data
                message_info['format'] = 'json'
                self.stats['json_messages'] += 1
                
                # 提取统计信息
                self._extract_stats(parsed_data)
                
            except json.JSONDecodeError:
                message_info['format'] = 'text'
                self.stats['error_messages'] += 1
        else:
            message_info['format'] = 'binary'
            self.stats['binary_messages'] += 1
        
        # 添加到最近数据缓存
        self.recent_data.append(message_info)
        
        # 保存到文件
        self._save_message(message_info)
        
        # 实时显示
        self._display_message(message_info)
    
    def _extract_stats(self, data):
        """提取统计信息"""
        try:
            # 设备信息
            if 'device' in data:
                self.stats['devices'].add(data['device'])
            
            # 传感器组信息
            if 'sensor_group' in data:
                self.stats['sensor_groups'].add(data['sensor_group'])
            
            # 数值指标
            for key, value in data.items():
                if isinstance(value, (int, float)):
                    self.stats['metrics'][key].append(value)
                    # 只保留最近100个值
                    if len(self.stats['metrics'][key]) > 100:
                        self.stats['metrics'][key] = self.stats['metrics'][key][-100:]
            
            # 嵌套数据处理
            if isinstance(data, dict):
                for key, value in data.items():
                    if isinstance(value, dict):
                        self._extract_nested_stats(value, f"{key}.")
        
        except Exception as e:
            self.logger.error(f"提取统计信息失败: {e}")
    
    def _extract_nested_stats(self, data, prefix=""):
        """提取嵌套数据的统计信息"""
        for key, value in data.items():
            full_key = f"{prefix}{key}"
            if isinstance(value, (int, float)):
                self.stats['metrics'][full_key].append(value)
                if len(self.stats['metrics'][full_key]) > 100:
                    self.stats['metrics'][full_key] = self.stats['metrics'][full_key][-100:]
            elif isinstance(value, dict):
                self._extract_nested_stats(value, f"{full_key}.")
    
    def _display_message(self, message_info):
        """实时显示消息"""
        timestamp_str = message_info['timestamp'].strftime('%H:%M:%S.%f')[:-3]
        
        print(f"\n📊 [{timestamp_str}] 消息 #{message_info['id']}")
        print(f"🔗 来源: {message_info['client']}")
        print(f"📏 大小: {message_info['size']} 字节")
        print(f"🏷️  格式: {message_info['format']}")
        
        if message_info['format'] == 'json' and message_info['parsed_data']:
            data = message_info['parsed_data']
            
            # 显示关键信息
            if 'device' in data:
                print(f"📱 设备: {data['device']}")
            if 'sensor_group' in data:
                print(f"📡 传感器组: {data['sensor_group']}")
            
            # 显示数值指标
            metrics = {k: v for k, v in data.items() if isinstance(v, (int, float))}
            if metrics:
                print("📈 指标:")
                for key, value in list(metrics.items())[:5]:  # 只显示前5个
                    print(f"   {key}: {value}")
                if len(metrics) > 5:
                    print(f"   ... 还有 {len(metrics) - 5} 个指标")
            
            # 显示嵌套数据概要
            nested = {k: v for k, v in data.items() if isinstance(v, dict)}
            if nested:
                print("📦 嵌套数据:")
                for key, value in nested.items():
                    print(f"   {key}: {len(value)} 个字段")
        
        elif message_info['format'] == 'text':
            print(f"📝 文本: {str(message_info['data'])[:100]}...")
        
        elif message_info['format'] == 'binary':
            print(f"🔢 二进制: {len(message_info['data'])} 字节")
        
        print("-" * 40)
    
    def _save_message(self, message_info):
        """保存消息到文件"""
        try:
            # 原始数据文件
            raw_filename = f"telemetry_raw_{datetime.now().strftime('%Y%m%d')}.log"
            with open(raw_filename, 'a', encoding='utf-8') as f:
                timestamp_str = message_info['timestamp'].strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
                f.write(f"[{timestamp_str}] {message_info['client']}: {message_info['data']}\n")
            
            # JSON 数据文件
            if message_info['format'] == 'json':
                json_filename = f"telemetry_json_{datetime.now().strftime('%Y%m%d')}.jsonl"
                with open(json_filename, 'a', encoding='utf-8') as f:
                    record = {
                        'timestamp': message_info['timestamp'].isoformat(),
                        'client': str(message_info['client']),
                        'data': message_info['parsed_data']
                    }
                    f.write(json.dumps(record, ensure_ascii=False) + '\n')
        
        except Exception as e:
            self.logger.error(f"保存消息失败: {e}")
    
    def _stats_reporter(self):
        """统计报告线程"""
        while self.running:
            time.sleep(30)  # 每30秒报告一次
            if self.running:
                self._print_stats()
    
    def _print_stats(self):
        """打印统计信息"""
        if not self.start_time:
            return
        
        runtime = datetime.now() - self.start_time
        
        print(f"\n📊 === 统计报告 ===")
        print(f"⏱️  运行时间: {runtime}")
        print(f"📨 总消息数: {self.stats['total_messages']}")
        print(f"📋 JSON 消息: {self.stats['json_messages']}")
        print(f"📝 文本消息: {self.stats['total_messages'] - self.stats['json_messages'] - self.stats['binary_messages']}")
        print(f"🔢 二进制消息: {self.stats['binary_messages']}")
        print(f"❌ 错误消息: {self.stats['error_messages']}")
        print(f"🔗 活跃连接: {len(self.clients)}")
        print(f"📱 设备数量: {len(self.stats['devices'])}")
        print(f"📡 传感器组: {len(self.stats['sensor_groups'])}")
        print(f"📈 监控指标: {len(self.stats['metrics'])}")
        
        # 消息速率
        if self.stats['total_messages'] > 0:
            rate = self.stats['total_messages'] / runtime.total_seconds()
            print(f"📊 平均速率: {rate:.2f} 消息/秒")
        
        print("=" * 40)
    
    def _real_time_display(self):
        """实时显示线程"""
        while self.running:
            time.sleep(5)  # 每5秒更新一次
            if self.running and self.stats['metrics']:
                self._display_metrics_summary()
    
    def _display_metrics_summary(self):
        """显示指标摘要"""
        print(f"\n📈 === 实时指标摘要 ===")
        
        for metric, values in list(self.stats['metrics'].items())[:10]:  # 显示前10个指标
            if values:
                latest = values[-1]
                avg = sum(values) / len(values)
                min_val = min(values)
                max_val = max(values)
                
                print(f"{metric}:")
                print(f"  最新: {latest}, 平均: {avg:.2f}, 范围: [{min_val}, {max_val}]")
        
        if len(self.stats['metrics']) > 10:
            print(f"... 还有 {len(self.stats['metrics']) - 10} 个指标")
        
        print("-" * 30)
    
    def _data_analyzer(self):
        """数据分析线程"""
        while self.running:
            time.sleep(60)  # 每分钟分析一次
            if self.running:
                self._analyze_patterns()
    
    def _analyze_patterns(self):
        """分析数据模式"""
        if len(self.recent_data) < 10:
            return
        
        print(f"\n🔍 === 数据模式分析 ===")
        
        # 分析消息频率
        json_count = sum(1 for msg in self.recent_data if msg['format'] == 'json')
        print(f"📊 最近 {len(self.recent_data)} 条消息中 JSON 占比: {json_count/len(self.recent_data)*100:.1f}%")
        
        # 分析设备活跃度
        device_activity = defaultdict(int)
        for msg in self.recent_data:
            if msg['format'] == 'json' and msg['parsed_data'] and 'device' in msg['parsed_data']:
                device_activity[msg['parsed_data']['device']] += 1
        
        if device_activity:
            print("📱 设备活跃度:")
            for device, count in sorted(device_activity.items(), key=lambda x: x[1], reverse=True):
                print(f"   {device}: {count} 条消息")
        
        print("-" * 30)
    
    def stop(self):
        """停止收集器"""
        self.running = False
        
        # 关闭所有连接
        for client in self.clients:
            try:
                client.close()
            except:
                pass
        self.clients.clear()
        
        # 关闭服务器
        if self.server_socket:
            try:
                self.server_socket.close()
            except:
                pass
        
        # 最终统计
        self._print_stats()
        print("🛑 收集器已停止")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='增强的 Telemetry 收集器')
    parser.add_argument('--host', default='***************', help='监听地址')
    parser.add_argument('--port', type=int, default=57400, help='监听端口')
    
    args = parser.parse_args()
    
    collector = EnhancedTelemetryCollector(args.host, args.port)
    
    try:
        collector.start()
    except KeyboardInterrupt:
        print("\n收到停止信号...")
        collector.stop()
    except Exception as e:
        print(f"收集器运行失败: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    import sys
    sys.exit(main())
