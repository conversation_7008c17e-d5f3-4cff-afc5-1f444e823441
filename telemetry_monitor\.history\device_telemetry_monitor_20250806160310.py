import grpc
import json
import threading
import logging
import signal
import os
import time
from datetime import datetime
from queue import Queue
from contextlib import contextmanager
from concurrent import futures
from google.protobuf.json_format import MessageToDict

# Import generated gRPC code
import telemetry_service_pb2 as telemetry_pb2
import telemetry_service_pb2_grpc as telemetry_pb2_grpc

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Global flag for graceful shutdown
running = True

# Signal handler for graceful shutdown
def signal_handler(signum, frame):
    global running
    logger.info("Received shutdown signal, stopping threads...")
    running = False

signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)

# Buffer for batch writing
class TelemetryBuffer:
    def __init__(self, filename, max_buffer_size=100):
        self.filename = filename
        self.buffer = []
        self.max_buffer_size = max_buffer_size
        self.lock = threading.Lock()
        
    def add(self, data):
        with self.lock:
            self.buffer.append(data)
            if len(self.buffer) >= self.max_buffer_size:
                self.flush()
                
    def flush(self):
        with self.lock:
            if self.buffer:
                with open(self.filename, "a") as f:
                    for data in self.buffer:
                        f.write(json.dumps(data) + "\n")
                self.buffer.clear()

@contextmanager
def grpc_channel_manager(address, port, timeout=5):
    """Context manager for gRPC channel with reconnection logic"""
    channel = None
    try:
        channel = grpc.insecure_channel(f"{address}:{port}")
        yield channel
    except Exception as e:
        logger.error(f"Channel error: {e}")
        raise
    finally:
        if channel:
            channel.close()

# Define a function to handle telemetry data for a single device
class TelemetryServicer(telemetry_pb2_grpc.TelemetryServiceServicer):
    def __init__(self, buffer, device_address):
        self.buffer = buffer
        self.device_address = device_address
        self.running = True

    def telemetrySubscribe(self, request, context):
        """处理来自设备的telemetry数据流"""
        logger.info(f"收到来自 {self.device_address} 的telemetry订阅请求")
        logger.info(f"订阅ID: {request.subscription_id}")
        logger.info(f"监控路径: {', '.join(request.sensor_paths)}")

        while context.is_active() and self.running:
            try:
                # 创建telemetry数据结构
                telemetry_data = {
                    "timestamp": datetime.utcnow().isoformat(),
                    "device": self.device_address,
                    "subscription": request.subscription_id,
                    "sensor_paths": request.sensor_paths,
                    "data": {}
                }

                # 对每个监控路径记录数据
                for path in request.sensor_paths:
                    telemetry_data["data"][path] = {
                        "status": "waiting for data",
                        "last_update": datetime.utcnow().isoformat()
                    }

                # 写入缓冲区
                self.buffer.add(telemetry_data)
                
                # 发送确认信息
                response = telemetry_pb2.TelemetryUpdate(
                    node_id=self.device_address,
                    subscription_id=request.subscription_id,
                    sensor_path=request.sensor_paths[0] if request.sensor_paths else "",
                    timestamp=int(time.time() * 1000),
                    payload=json.dumps(telemetry_data["data"]).encode('utf-8')
                )
                
                yield response
                logger.info(f"已发送数据确认到设备 {self.device_address}")
                time.sleep(request.sample_interval / 1000.0)  # 根据配置的采样间隔等待

            except Exception as e:
                logger.error(f"处理telemetry数据时出错: {str(e)}")
                context.abort(grpc.StatusCode.INTERNAL, f"处理错误: {str(e)}")
                break

        # 清理工作
        self.buffer.flush()
        logger.info(f"Telemetry订阅已结束: {request.subscription_id}")

    def stop(self):
        self.running = False
        self.buffer.flush()

from typing import Dict, Any

def monitor_device(device_config: Dict[str, Any]) -> None:
    """
    Starts a gRPC server to receive telemetry data from the device.
    :param device_config: Dictionary containing device configuration
    """
    device_address = device_config["address"]
    listen_address = device_config["server_address"]
    listen_port = device_config["port"]
    output_file = device_config["output_file"]

    # 确保输出目录存在
    output_dir = os.path.dirname(output_file)
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)

    logger.info(f"[Device {device_address}] Initializing telemetry server...")
    buffer = TelemetryBuffer(output_file)
    try:
        # Create the gRPC server
        server = grpc.server(futures.ThreadPoolExecutor(max_workers=10))
        servicer = TelemetryServicer(buffer, device_address)
        telemetry_pb2_grpc.add_TelemetryServiceServicer_to_server(servicer, server)

        # Start server
        server_addr = f"{listen_address}:{listen_port}"
        port_result = server.add_insecure_port(server_addr)
        if port_result == 0:
            logger.error(f"[Device {device_address}] Failed to bind to {server_addr}. Exiting thread.")
            return
        server.start()
        logger.info(f"[Device {device_address}] gRPC server started, listening on {server_addr}")

        # Wait for telemetry data to arrive
        while running:
            time.sleep(1)
    except Exception as e:
        logger.exception(f"[Device {device_address}] Exception in monitor_device: {e}")
    finally:
        logger.info(f"[Device {device_address}] Shutting down server...")
        try:
            servicer.stop()
            server.stop(0)
        except Exception as e:
            logger.error(f"[Device {device_address}] Error during server shutdown: {e}")
        buffer.flush()
        logger.info(f"[Device {device_address}] Telemetry server stopped and buffer flushed.")

# List of devices to monitor
SENSOR_PATHS = [
    "/openconfig-terminal-device:terminal-device/logical-channels/channel/ethernet/state",
    "/openconfig-platform:components/component/state",
    "/openconfig-terminal-device:terminal-device/logical-channels/channel/otn/state",
    "/openconfig-platform:components/component/openconfig-platform-transceiver:transceiver/physical-channels/channel/state",
    "/openconfig-platform:components/component/openconfig-terminal-device:optical-channel/state",
    "/openconfig-platform:components/component/openconfig-platform-transceiver:transceiver/state",
    "/openconfig-platform:components/component/cpu/openconfig-platform-cpu:utilization"
]

devices = [
    {
        "address": "***************",         # Device sending telemetry
        "server_address": "0.0.0.0",          # Listen on all interfaces
        "port": 50051,                        # gRPC server port for listening
        "output_file": "telemetry_data/device_***************.json",
        "subscription_id": "allPsg",
        "sample_interval": 1000,
        "heartbeat_interval": 15000,
        "sensor_paths": SENSOR_PATHS
    }
]


def main():
    global running
    # Create telemetry_data directory if it doesn't exist
    os.makedirs("telemetry_data", exist_ok=True)

    # Create and start a thread for each device
    threads = []
    for device in devices:
        t = threading.Thread(
            target=monitor_device,
            args=(device,),
            name=f"MonitorThread-{device['address']}"
        )
        threads.append(t)
        t.start()
        logger.info(f"Started monitoring thread for device {device['address']}")

    # Wait for all threads to complete
    try:
        for t in threads:
            t.join()
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt, initiating shutdown...")
        running = False
        for t in threads:
            t.join()
    except Exception as e:
        logger.exception(f"Exception in main thread: {e}")

    logger.info("All monitoring threads have stopped. Exiting...")


if __name__ == "__main__":
    main()
