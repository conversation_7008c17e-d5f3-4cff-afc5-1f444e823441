{"message_number": 1, "timestamp": "2025-08-11 11:02:52.747000", "raw_timestamp_ns": 1754881372747000000, "device_ip": "***************", "message_type": "update", "prefix": "/openconfig-platform:components", "alias": "", "atomic": false, "updates": [{"path": "/component[name=TRANSCEIVER-1-5-C1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-12]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-33]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-5]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C3]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-4-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-5-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-12]/cpu/openconfig-platform-cpu:utilization", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, {"path": "/component[name=CHASSIS-1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-4-C1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-34]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-7-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-32]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-11]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-4-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-4-C1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C5]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C3]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=OLP-1-3]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OLP-1-3"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OLP-1-3"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C3]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C5]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=PSU-1-22]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-1-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-31]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}}, {"path": "/component[name=PSU-1-21]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-8]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-4-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-7]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C3]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-4-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-4-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-4-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-4"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-8-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-11]/cpu/openconfig-platform-cpu:utilization", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, {"path": "/component[name=TRANSCEIVER-1-4-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}], "deletes": [], "raw_notification": {"timestamp": "1754881372747000000", "prefix": {"elem": [{"name": "openconfig-platform:components"}]}, "update": [{"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OLP-1-3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-4-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}]}}
{"message_number": 2, "timestamp": "2025-08-11 11:02:53.747000", "raw_timestamp_ns": 1754881373747000000, "device_ip": "***************", "message_type": "update", "prefix": "/openconfig-platform:components", "alias": "", "atomic": false, "updates": [{"path": "/component[name=TRANSCEIVER-1-5-C1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-12]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-33]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-5]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C3]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-4-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-5-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-12]/cpu/openconfig-platform-cpu:utilization", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, {"path": "/component[name=CHASSIS-1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-4-C1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-34]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-7-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-32]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-11]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-4-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-4-C1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C5]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C3]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=OLP-1-3]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OLP-1-3"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OLP-1-3"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C3]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C5]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=PSU-1-22]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-1-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-31]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}}, {"path": "/component[name=PSU-1-21]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-8]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-4-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-7]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C3]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-4-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-4-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-4-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-4"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-8-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-11]/cpu/openconfig-platform-cpu:utilization", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, {"path": "/component[name=TRANSCEIVER-1-4-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}], "deletes": [], "raw_notification": {"timestamp": "1754881373747000000", "prefix": {"elem": [{"name": "openconfig-platform:components"}]}, "update": [{"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OLP-1-3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-4-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}]}}
{"message_number": 3, "timestamp": "2025-08-11 11:02:54.747000", "raw_timestamp_ns": 1754881374747000000, "device_ip": "***************", "message_type": "update", "prefix": "/openconfig-platform:components", "alias": "", "atomic": false, "updates": [{"path": "/component[name=TRANSCEIVER-1-5-C1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-12]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-33]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-5]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C3]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-4-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-5-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-12]/cpu/openconfig-platform-cpu:utilization", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, {"path": "/component[name=CHASSIS-1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-4-C1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-34]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-7-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-32]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-11]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-4-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-4-C1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C5]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C3]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=OLP-1-3]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OLP-1-3"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OLP-1-3"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C3]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C5]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=PSU-1-22]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-1-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-31]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}}, {"path": "/component[name=PSU-1-21]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-8]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-4-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-7]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C3]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-4-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-4-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-4-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-4"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-8-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-11]/cpu/openconfig-platform-cpu:utilization", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, {"path": "/component[name=TRANSCEIVER-1-4-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}], "deletes": [], "raw_notification": {"timestamp": "1754881374747000000", "prefix": {"elem": [{"name": "openconfig-platform:components"}]}, "update": [{"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OLP-1-3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-4-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}]}}
{"message_number": 4, "timestamp": "2025-08-11 11:02:55.747000", "raw_timestamp_ns": 1754881375747000000, "device_ip": "***************", "message_type": "update", "prefix": "/openconfig-platform:components", "alias": "", "atomic": false, "updates": [{"path": "/component[name=TRANSCEIVER-1-5-C1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-12]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-33]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-5]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C3]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-4-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-5-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-12]/cpu/openconfig-platform-cpu:utilization", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, {"path": "/component[name=CHASSIS-1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-4-C1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-34]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-7-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-32]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-11]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-4-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-4-C1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C5]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C3]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=OLP-1-3]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OLP-1-3"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OLP-1-3"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C3]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C5]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=PSU-1-22]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-1-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-31]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}}, {"path": "/component[name=PSU-1-21]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-8]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-4-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-7]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C3]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-4-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-4-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-4-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-4"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-8-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-11]/cpu/openconfig-platform-cpu:utilization", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, {"path": "/component[name=TRANSCEIVER-1-4-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}], "deletes": [], "raw_notification": {"timestamp": "1754881375747000000", "prefix": {"elem": [{"name": "openconfig-platform:components"}]}, "update": [{"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OLP-1-3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-4-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}]}}
{"message_number": 5, "timestamp": "2025-08-11 11:02:56.747000", "raw_timestamp_ns": 1754881376747000000, "device_ip": "***************", "message_type": "update", "prefix": "/openconfig-platform:components", "alias": "", "atomic": false, "updates": [{"path": "/component[name=TRANSCEIVER-1-5-C1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-12]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-33]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-5]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C3]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-4-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-5-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-12]/cpu/openconfig-platform-cpu:utilization", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, {"path": "/component[name=CHASSIS-1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-4-C1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-34]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-7-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-32]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-11]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-4-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-4-C1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C5]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C3]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=OLP-1-3]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OLP-1-3"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OLP-1-3"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C3]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C5]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=PSU-1-22]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-1-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-31]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}}, {"path": "/component[name=PSU-1-21]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-8]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-4-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-7]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C3]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-4-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-4-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-4-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-4"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-8-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-11]/cpu/openconfig-platform-cpu:utilization", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, {"path": "/component[name=TRANSCEIVER-1-4-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}], "deletes": [], "raw_notification": {"timestamp": "1754881376747000000", "prefix": {"elem": [{"name": "openconfig-platform:components"}]}, "update": [{"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OLP-1-3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-4-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}]}}
{"message_number": 6, "timestamp": "2025-08-11 11:02:57.747000", "raw_timestamp_ns": 1754881377747000000, "device_ip": "***************", "message_type": "update", "prefix": "/openconfig-platform:components", "alias": "", "atomic": false, "updates": [{"path": "/component[name=TRANSCEIVER-1-5-C1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-12]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-33]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-5]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C3]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-4-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-5-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-12]/cpu/openconfig-platform-cpu:utilization", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, {"path": "/component[name=CHASSIS-1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-4-C1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-34]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-7-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-32]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-11]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-4-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-4-C1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C5]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C3]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=OLP-1-3]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OLP-1-3"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OLP-1-3"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C3]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C5]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=PSU-1-22]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-1-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-31]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}}, {"path": "/component[name=PSU-1-21]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-8]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-4-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-7]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C3]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-4-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-4-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-4-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-4"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-8-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-11]/cpu/openconfig-platform-cpu:utilization", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, {"path": "/component[name=TRANSCEIVER-1-4-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}], "deletes": [], "raw_notification": {"timestamp": "1754881377747000000", "prefix": {"elem": [{"name": "openconfig-platform:components"}]}, "update": [{"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OLP-1-3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-4-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}]}}
{"message_number": 7, "timestamp": "2025-08-11 11:02:58.747000", "raw_timestamp_ns": 1754881378747000000, "device_ip": "***************", "message_type": "update", "prefix": "/openconfig-platform:components", "alias": "", "atomic": false, "updates": [{"path": "/component[name=TRANSCEIVER-1-5-C1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-12]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-33]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-5]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C3]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-4-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-5-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-12]/cpu/openconfig-platform-cpu:utilization", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, {"path": "/component[name=CHASSIS-1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-4-C1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-34]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-7-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-32]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-11]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-4-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-4-C1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C5]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C3]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=OLP-1-3]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OLP-1-3"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OLP-1-3"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C3]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C5]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=PSU-1-22]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-1-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-31]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}}, {"path": "/component[name=PSU-1-21]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-8]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-4-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-7]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C3]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-4-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-4-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-4-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-4"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-8-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-11]/cpu/openconfig-platform-cpu:utilization", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, {"path": "/component[name=TRANSCEIVER-1-4-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}], "deletes": [], "raw_notification": {"timestamp": "1754881378747000000", "prefix": {"elem": [{"name": "openconfig-platform:components"}]}, "update": [{"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OLP-1-3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-4-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}]}}
{"message_number": 8, "timestamp": "2025-08-11 11:02:59.747000", "raw_timestamp_ns": 1754881379747000000, "device_ip": "***************", "message_type": "update", "prefix": "/openconfig-platform:components", "alias": "", "atomic": false, "updates": [{"path": "/component[name=TRANSCEIVER-1-5-C1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-12]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-33]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-5]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C3]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-4-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-5-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-12]/cpu/openconfig-platform-cpu:utilization", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, {"path": "/component[name=CHASSIS-1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-4-C1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-34]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-7-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-32]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-11]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-4-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-4-C1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C5]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C3]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=OLP-1-3]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OLP-1-3"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OLP-1-3"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C3]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C5]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=PSU-1-22]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-1-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-31]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}}, {"path": "/component[name=PSU-1-21]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-8]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-4-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-7]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C3]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-4-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-4-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-4-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-4"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-8-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-11]/cpu/openconfig-platform-cpu:utilization", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, {"path": "/component[name=TRANSCEIVER-1-4-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}], "deletes": [], "raw_notification": {"timestamp": "1754881379747000000", "prefix": {"elem": [{"name": "openconfig-platform:components"}]}, "update": [{"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OLP-1-3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-4-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}]}}
{"message_number": 9, "timestamp": "2025-08-11 11:03:00.747000", "raw_timestamp_ns": 1754881380747000000, "device_ip": "***************", "message_type": "update", "prefix": "/openconfig-platform:components", "alias": "", "atomic": false, "updates": [{"path": "/component[name=TRANSCEIVER-1-5-C1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-12]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-33]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-5]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C3]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-4-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-5-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-12]/cpu/openconfig-platform-cpu:utilization", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, {"path": "/component[name=CHASSIS-1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-4-C1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-34]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-7-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-32]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-11]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-4-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-4-C1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C5]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C3]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=OLP-1-3]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OLP-1-3"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OLP-1-3"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C3]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C5]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=PSU-1-22]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-1-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-31]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}}, {"path": "/component[name=PSU-1-21]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-8]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-4-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-7]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C3]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-4-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-4-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-4-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-4"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-8-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-11]/cpu/openconfig-platform-cpu:utilization", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, {"path": "/component[name=TRANSCEIVER-1-4-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}], "deletes": [], "raw_notification": {"timestamp": "1754881380747000000", "prefix": {"elem": [{"name": "openconfig-platform:components"}]}, "update": [{"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OLP-1-3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-4-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}]}}
{"message_number": 10, "timestamp": "2025-08-11 11:03:01.747000", "raw_timestamp_ns": 1754881381747000000, "device_ip": "***************", "message_type": "update", "prefix": "/openconfig-platform:components", "alias": "", "atomic": false, "updates": [{"path": "/component[name=TRANSCEIVER-1-5-C1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-12]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-33]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-5]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C3]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-4-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-5-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-12]/cpu/openconfig-platform-cpu:utilization", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, {"path": "/component[name=CHASSIS-1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-4-C1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-34]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-7-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-32]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-11]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-4-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-4-C1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C5]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C3]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=OLP-1-3]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OLP-1-3"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OLP-1-3"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C3]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C5]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=PSU-1-22]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-1-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-31]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}}, {"path": "/component[name=PSU-1-21]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-8]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-4-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-7]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C3]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-4-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-4-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-4-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-4"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-8-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-11]/cpu/openconfig-platform-cpu:utilization", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, {"path": "/component[name=TRANSCEIVER-1-4-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}], "deletes": [], "raw_notification": {"timestamp": "1754881381747000000", "prefix": {"elem": [{"name": "openconfig-platform:components"}]}, "update": [{"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OLP-1-3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-4-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}]}}
{"message_number": 11, "timestamp": "2025-08-11 11:03:02.747000", "raw_timestamp_ns": 1754881382747000000, "device_ip": "***************", "message_type": "update", "prefix": "/openconfig-platform:components", "alias": "", "atomic": false, "updates": [{"path": "/component[name=TRANSCEIVER-1-5-C1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-12]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-33]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-5]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C3]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-4-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-5-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-12]/cpu/openconfig-platform-cpu:utilization", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, {"path": "/component[name=CHASSIS-1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-4-C1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-34]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-7-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-32]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-11]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-4-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-4-C1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C5]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C3]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=OLP-1-3]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OLP-1-3"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OLP-1-3"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C3]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C5]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=PSU-1-22]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-1-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-31]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}}, {"path": "/component[name=PSU-1-21]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-8]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-4-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-7]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C3]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-4-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-4-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-4-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-4"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-8-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-11]/cpu/openconfig-platform-cpu:utilization", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, {"path": "/component[name=TRANSCEIVER-1-4-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}], "deletes": [], "raw_notification": {"timestamp": "1754881382747000000", "prefix": {"elem": [{"name": "openconfig-platform:components"}]}, "update": [{"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OLP-1-3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-4-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}]}}
{"message_number": 12, "timestamp": "2025-08-11 11:03:03.747000", "raw_timestamp_ns": 1754881383747000000, "device_ip": "***************", "message_type": "update", "prefix": "/openconfig-platform:components", "alias": "", "atomic": false, "updates": [{"path": "/component[name=TRANSCEIVER-1-5-C1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-12]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-33]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-5]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C3]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-4-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-5-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-12]/cpu/openconfig-platform-cpu:utilization", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, {"path": "/component[name=CHASSIS-1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-4-C1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-34]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-7-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-32]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-11]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-4-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-4-C1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C5]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C3]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=OLP-1-3]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OLP-1-3"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OLP-1-3"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C3]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-1-C5]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=PSU-1-22]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-1-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=FAN-1-31]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}}, {"path": "/component[name=PSU-1-21]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-L1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-8]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-4-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-7]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-8-L1]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C3]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-4-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-4-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-4-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-4"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-7-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=TRANSCEIVER-1-5-C4]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "state"}]}}}, {"path": "/component[name=OCH-1-8-L1]/openconfig-terminal-device:optical-channel/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}}, {"path": "/component[name=LINECARD-1-1]/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}}, {"path": "/component[name=MCU-1-11]/cpu/openconfig-platform-cpu:utilization", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}}, {"path": "/component[name=TRANSCEIVER-1-4-C4]/openconfig-platform-transceiver:transceiver/state", "value": {"error": "Update.value field not set", "raw_update_content": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}, "raw_update": {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}}], "deletes": [], "raw_notification": {"timestamp": "1754881383747000000", "prefix": {"elem": [{"name": "openconfig-platform:components"}]}, "update": [{"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-33"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-5-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-12"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "CHASSIS-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-34"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-7-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-32"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OLP-1-3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-1-C5"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-22"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-1-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "FAN-1-31"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "PSU-1-21"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-L1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-8"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-7"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-8-L1"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C3"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-4-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-7-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-5-C4"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "OCH-1-8-L1"}}, {"name": "openconfig-terminal-device:optical-channel"}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "LINECARD-1-1"}}, {"name": "state"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "MCU-1-11"}}, {"name": "cpu"}, {"name": "openconfig-platform-cpu:utilization"}]}}, {"path": {"elem": [{"name": "component", "key": {"name": "TRANSCEIVER-1-4-C4"}}, {"name": "openconfig-platform-transceiver:transceiver"}, {"name": "state"}]}}]}}
