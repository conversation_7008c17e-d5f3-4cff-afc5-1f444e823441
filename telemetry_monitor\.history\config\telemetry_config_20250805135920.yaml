# Telemetry 配置模板
collectors:
  # 默认收集器 - 本地测试收集器
  - name: "default-collector"
    address: "***************"
    port: 57400
    protocol: "grpc"
    encoding: "json"

  # 备用收集器
  - name: "backup-collector"
    address: "***************"
    port: 57401
    protocol: "grpc"
    encoding: "json"

# 监控指标模板定义
monitoring_templates:
  # 基础监控模板
  basic:
    description: "基础系统和接口监控"
    categories:
      - "system_basic"
      - "interface_basic"
    sample_interval: 30000  # 30秒
    
  # 高级监控模板
  advanced:
    description: "包含路由协议的高级监控"
    categories:
      - "system_all"
      - "interface_all"
      - "routing_protocols"
    sample_interval: 15000  # 15秒
    
  # 企业级监控模板
  enterprise:
    description: "全面的企业级监控"
    categories:
      - "system_all"
      - "interface_all"
      - "routing_protocols"
      - "hardware_monitoring"
      - "qos_monitoring"
      - "mpls_vpn"
    sample_interval: 10000  # 10秒
    
  # 性能监控模板
  performance:
    description: "专注于性能指标的监控"
    categories:
      - "system_performance"
      - "interface_performance"
      - "qos_monitoring"
    sample_interval: 1000   # 1秒

# 监控类别定义
monitoring_categories:
  # 系统监控类别
  system_basic:
    - "cpu_utilization"
    - "memory_utilization"
    
  system_all:
    - "cpu_utilization"
    - "memory_utilization"
    - "storage_utilization"
    - "system_temperature"
    
  system_performance:
    - "cpu_detailed"
    - "memory_detailed"
    - "process_stats"
    
  # 接口监控类别
  interface_basic:
    - "interface_traffic"
    - "interface_status"
    
  interface_all:
    - "interface_traffic"
    - "interface_status"
    - "interface_errors"
    - "optical_stats"
    
  interface_performance:
    - "interface_detailed_stats"
    - "interface_queue_stats"
    
  # 路由协议监控
  routing_protocols:
    - "bgp_neighbors"
    - "ospf_neighbors"
    - "isis_adjacency"
    - "routing_table_stats"
    
  # 硬件监控
  hardware_monitoring:
    - "fan_status"
    - "power_supply"
    - "temperature_sensors"
    - "hardware_alarms"
    
  # QoS 监控
  qos_monitoring:
    - "queue_statistics"
    - "policy_statistics"
    - "traffic_classification"
    - "bandwidth_utilization"
    
  # MPLS/VPN 监控
  mpls_vpn:
    - "lsp_statistics"
    - "vrf_statistics"
    - "mpls_labels"
    - "vpn_connections"

# 默认配置
default_settings:
  # 默认采样间隔（毫秒）
  sample_interval: 30000
  # 默认收集器
  default_collector: "default-collector"
  # 默认监控模板
  default_template: "basic"
  # 是否启用压缩
  enable_compression: true
  # 最大重试次数
  max_retries: 3