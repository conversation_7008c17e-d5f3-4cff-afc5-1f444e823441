# Telemetry gRPC Server 优化说明

## 优化概述

本次优化主要解决了以下问题：

1. **完整解析所有sensor path消息** - 改进了数据解析逻辑，确保不遗漏任何路径的数据
2. **结构化JSON输出** - 按照要求的格式生成JSON文件
3. **数据类型处理** - 正确处理各种数据类型（字符串、数字、JSON等）
4. **路径标准化** - 移除namespace前缀，保留核心路径信息

## 主要改进

### 1. 新增设备配置映射

```python
DEVICE_SUBSCRIPTIONS = {
    "***************": {
        "subscription_name": "allPsg",
        "sensor_group": "allSg", 
        "sensor_paths": [
            "/openconfig-terminal-device:terminal-device/logical-channels/channel/ethernet/state",
            "/openconfig-platform:components/component/state",
            # ... 其他路径
        ]
    }
}
```

### 2. 结构化数据处理

新增了专门的数据处理函数：

- `create_structured_telemetry_data()` - 创建符合要求的JSON结构
- `process_ethernet_state()` - 处理以太网状态数据
- `process_transceiver_physical_channel_state()` - 处理收发器物理通道数据
- `process_component_state()` - 处理组件状态数据
- `process_optical_channel_state()` - 处理光通道状态数据
- `process_transceiver_state()` - 处理收发器状态数据
- `process_cpu_utilization()` - 处理CPU利用率数据
- `process_otn_state()` - 处理OTN状态数据

### 3. 路径标准化

`normalize_sensor_path()` 函数移除openconfig namespace前缀：

```
原始: /openconfig-terminal-device:terminal-device/logical-channels/channel/ethernet/state
标准化: /terminal-device/logical-channels/channel/ethernet/state
```

### 4. 优化的数据处理流程

在 `_handle_update()` 方法中：

1. 提取所有更新项的路径和值
2. 按路径分组数据
3. 生成结构化JSON输出
4. 保存到专门的结构化数据文件

## 输出文件

优化后的服务器会生成以下文件：

1. **原始数据文件**: `{device_ip}_{date}.json` - 保留原始protobuf数据
2. **结构化数据文件**: `{device_ip}_structured_{date}.json` - 按要求格式的JSON数据
3. **最新数据文件**: `{device_ip}_latest_structured.json` - 最新的结构化数据（覆盖模式）

## JSON输出格式

```json
{
  "timestamp": "2025-08-11T10:57:22.634509",
  "device_id": "***************", 
  "subscription": "allPsg",
  "sensor_paths": {
    "/terminal-device/logical-channels/channel/ethernet/state": {
      "counters": {
        "in-octets": 6164784,
        "out-octets": 4168902,
        "in-pkts": 9468,
        "out-pkts": 7604
      }
    },
    "/components/component/transceiver/physical-channels/channel/state": {
      "output-power": -2.52,
      "input-power": -14.41,
      "laser-bias-current": 29.89,
      "temperature": 34.86
    },
    "/components/component/state": {
      "temperature": {
        "instant": 28.23,
        "avg": 31.06,
        "max": 40.07
      },
      "memory": {
        "utilized": 5880,
        "available": 14998
      }
    }
  }
}
```

## 使用方法

1. **启动服务器**:
   ```bash
   python telemetry_grpc_server.py
   ```

2. **查看结构化数据**:
   - 实时数据: `telemetry_data/{device_ip}_latest_structured.json`
   - 历史数据: `telemetry_data/{device_ip}_structured_{date}.json`

3. **测试功能**:
   ```bash
   python test_optimized_server.py
   ```

## 处理的数据类型

### 以太网状态 (ethernet/state)
- 计数器: in-octets, out-octets, in-pkts, out-pkts

### 收发器物理通道状态 (transceiver/physical-channels/channel/state)
- 功率: output-power, input-power
- 电流: laser-bias-current
- 温度: temperature

### 组件状态 (components/component/state)
- 温度: instant, avg, max, min
- 内存: utilized, available

### 光通道状态 (optical-channel/state)
- 频率: frequency
- 目标输出功率: target-output-power
- 操作模式: operational-mode

### 收发器状态 (transceiver/state)
- 功率和温度相关参数

### CPU利用率 (cpu/utilization)
- 利用率统计信息

## 调试功能

- 设置 `debug_mode = True` 在 `get_json_value()` 函数中可以启用详细调试信息
- 原始消息会打印到控制台，便于分析数据结构
- 支持多种编码格式的数据解析

## 注意事项

1. 确保设备IP在 `DEVICE_SUBSCRIPTIONS` 中有配置
2. 如果遇到新的数据格式，可以在相应的处理函数中添加支持
3. 结构化数据文件会持续追加，注意磁盘空间
4. 最新数据文件会被覆盖，适合实时监控使用

## 故障排除

如果发现数据遗漏：

1. 检查控制台输出，查看是否有解析错误
2. 查看原始数据文件，确认数据是否接收到
3. 检查路径是否在预期的sensor_paths列表中
4. 启用调试模式查看详细的数据解析过程
