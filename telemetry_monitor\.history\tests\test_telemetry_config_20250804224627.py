#!/usr/bin/env python3
"""
Telemetry 配置管理器测试
"""

import unittest
import tempfile
import os
import sys
import yaml

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.telemetry_config import TelemetryConfigurator, CollectorConfig, SensorGroupConfig, SubscriptionConfig


class TestTelemetryConfigurator(unittest.TestCase):
    """Telemetry 配置管理器测试类"""
    
    def setUp(self):
        """测试初始化"""
        # 创建临时配置文件
        self.temp_dir = tempfile.mkdtemp()
        
        # 创建设备配置文件
        self.device_config = {
            'devices': [
                {
                    'name': 'test-device',
                    'host': '***********',
                    'port': 830,
                    'username': 'admin',
                    'password': 'password',
                    'timeout': 30,
                    'device_type': 'generic'
                }
            ],
            'connection_settings': {
                'default_timeout': 30,
                'retry_count': 3,
                'retry_interval': 5,
                'verify_ssl': False,
                'session_keepalive': 60
            }
        }
        
        self.device_config_file = os.path.join(self.temp_dir, 'device_config.yaml')
        with open(self.device_config_file, 'w') as f:
            yaml.dump(self.device_config, f)
        
        # 创建 Telemetry 配置文件
        self.telemetry_config = {
            'collectors': [
                {
                    'name': 'test-collector',
                    'address': '***********00',
                    'port': 57400,
                    'protocol': 'grpc',
                    'encoding': 'json'
                }
            ],
            'monitoring_templates': {
                'test_template': {
                    'description': '测试模板',
                    'categories': ['system_basic', 'interface_basic'],
                    'sample_interval': 30000
                }
            },
            'monitoring_categories': {
                'system_basic': ['cpu_utilization', 'memory_utilization'],
                'interface_basic': ['interface_traffic', 'interface_status']
            }
        }
        
        self.telemetry_config_file = os.path.join(self.temp_dir, 'telemetry_config.yaml')
        with open(self.telemetry_config_file, 'w') as f:
            yaml.dump(self.telemetry_config, f)
        
        # 初始化配置器
        self.configurator = TelemetryConfigurator(
            device_config_file=self.device_config_file,
            telemetry_config_file=self.telemetry_config_file
        )
    
    def tearDown(self):
        """测试清理"""
        # 清理临时文件
        import shutil
        shutil.rmtree(self.temp_dir)
        
        # 清理配置器
        self.configurator.cleanup()
    
    def test_init(self):
        """测试初始化"""
        self.assertIsNotNone(self.configurator.netconf_client)
        self.assertIsNotNone(self.configurator.yang_templates)
        self.assertIn('test-collector', self.configurator.collectors)
    
    def test_add_collector(self):
        """测试添加收集器"""
        self.configurator.add_collector(
            name='new-collector',
            address='*************',
            port=57401,
            protocol='tcp',
            encoding='protobuf'
        )
        
        self.assertIn('new-collector', self.configurator.collectors)
        collector = self.configurator.collectors['new-collector']
        self.assertEqual(collector.address, '*************')
        self.assertEqual(collector.port, 57401)
        self.assertEqual(collector.protocol, 'tcp')
        self.assertEqual(collector.encoding, 'protobuf')
    
    def test_create_sensor_group(self):
        """测试创建传感器组"""
        sg = self.configurator.create_sensor_group(
            group_id='test-sg',
            category='system',
            metrics=['cpu_utilization', 'memory_utilization'],
            description='测试传感器组'
        )
        
        self.assertIsInstance(sg, SensorGroupConfig)
        self.assertEqual(sg.group_id, 'test-sg')
        self.assertEqual(sg.category, 'system')
        self.assertEqual(sg.metrics, ['cpu_utilization', 'memory_utilization'])
        self.assertGreater(len(sg.paths), 0)
        self.assertIn('test-sg', self.configurator.sensor_groups)
    
    def test_create_sensor_groups_from_template(self):
        """测试从模板创建传感器组"""
        sensor_groups = self.configurator.create_sensor_groups_from_template('test_template')
        
        self.assertIsInstance(sensor_groups, list)
        self.assertGreater(len(sensor_groups), 0)
        
        for sg in sensor_groups:
            self.assertIsInstance(sg, SensorGroupConfig)
            self.assertTrue(sg.group_id.startswith('test_template_'))
    
    def test_create_subscription(self):
        """测试创建订阅"""
        subscription = self.configurator.create_subscription(
            subscription_id='test-sub',
            sensor_group='test-sg',
            destination_group='test-collector',
            sample_interval=15000
        )
        
        self.assertIsInstance(subscription, SubscriptionConfig)
        self.assertEqual(subscription.subscription_id, 'test-sub')
        self.assertEqual(subscription.sensor_group, 'test-sg')
        self.assertEqual(subscription.destination_group, 'test-collector')
        self.assertEqual(subscription.sample_interval, 15000)
        self.assertIn('test-sub', self.configurator.subscriptions)
    
    def test_list_available_templates(self):
        """测试列出可用模板"""
        templates = self.configurator.list_available_templates()
        self.assertIsInstance(templates, list)
        self.assertIn('test_template', templates)
    
    def test_list_available_categories(self):
        """测试列出可用类别"""
        categories = self.configurator.list_available_categories()
        self.assertIsInstance(categories, list)
        self.assertIn('system_basic', categories)
        self.assertIn('interface_basic', categories)
    
    def test_get_template_info(self):
        """测试获取模板信息"""
        info = self.configurator.get_template_info('test_template')
        
        self.assertEqual(info['name'], 'test_template')
        self.assertEqual(info['description'], '测试模板')
        self.assertEqual(info['categories'], ['system_basic', 'interface_basic'])
        self.assertEqual(info['sample_interval'], 30000)
        self.assertGreater(info['total_metrics'], 0)
    
    def test_get_template_info_invalid(self):
        """测试获取无效模板信息"""
        with self.assertRaises(ValueError):
            self.configurator.get_template_info('invalid_template')
    
    def test_configure_custom_telemetry_validation(self):
        """测试自定义配置验证"""
        # 测试无效类别
        result = self.configurator.configure_custom_telemetry(
            device_name='test-device',
            categories=['invalid_category'],
            collector_address='***********00',
            collector_port=57400,
            sample_interval=30000
        )
        
        # 由于设备连接会失败，这里主要测试参数验证
        self.assertFalse(result)


class TestCollectorConfig(unittest.TestCase):
    """收集器配置测试类"""
    
    def test_collector_config_creation(self):
        """测试收集器配置创建"""
        collector = CollectorConfig(
            name='test-collector',
            address='***********00',
            port=57400,
            protocol='grpc',
            encoding='json'
        )
        
        self.assertEqual(collector.name, 'test-collector')
        self.assertEqual(collector.address, '***********00')
        self.assertEqual(collector.port, 57400)
        self.assertEqual(collector.protocol, 'grpc')
        self.assertEqual(collector.encoding, 'json')


class TestSensorGroupConfig(unittest.TestCase):
    """传感器组配置测试类"""
    
    def test_sensor_group_config_creation(self):
        """测试传感器组配置创建"""
        paths = [
            '/openconfig-system:system/cpus/cpu/state/total',
            '/openconfig-system:system/memory/state/physical'
        ]
        
        sg = SensorGroupConfig(
            group_id='test-sg',
            category='system',
            metrics=['cpu_utilization', 'memory_utilization'],
            paths=paths,
            description='测试传感器组'
        )
        
        self.assertEqual(sg.group_id, 'test-sg')
        self.assertEqual(sg.category, 'system')
        self.assertEqual(sg.metrics, ['cpu_utilization', 'memory_utilization'])
        self.assertEqual(sg.paths, paths)
        self.assertEqual(sg.description, '测试传感器组')


class TestSubscriptionConfig(unittest.TestCase):
    """订阅配置测试类"""
    
    def test_subscription_config_creation(self):
        """测试订阅配置创建"""
        subscription = SubscriptionConfig(
            subscription_id='test-sub',
            sensor_group='test-sg',
            destination_group='test-dest',
            sample_interval=30000,
            source_address='***********',
            qos_marking=1,
            suppress_redundant=True,
            heartbeat_interval=60000
        )
        
        self.assertEqual(subscription.subscription_id, 'test-sub')
        self.assertEqual(subscription.sensor_group, 'test-sg')
        self.assertEqual(subscription.destination_group, 'test-dest')
        self.assertEqual(subscription.sample_interval, 30000)
        self.assertEqual(subscription.source_address, '***********')
        self.assertEqual(subscription.qos_marking, 1)
        self.assertTrue(subscription.suppress_redundant)
        self.assertEqual(subscription.heartbeat_interval, 60000)
    
    def test_subscription_config_defaults(self):
        """测试订阅配置默认值"""
        subscription = SubscriptionConfig(
            subscription_id='test-sub',
            sensor_group='test-sg',
            destination_group='test-dest'
        )
        
        self.assertEqual(subscription.sample_interval, 30000)
        self.assertEqual(subscription.source_address, '')
        self.assertEqual(subscription.qos_marking, 0)
        self.assertFalse(subscription.suppress_redundant)
        self.assertEqual(subscription.heartbeat_interval, 60000)


if __name__ == '__main__':
    unittest.main()