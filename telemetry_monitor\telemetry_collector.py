#!/usr/bin/env python3
"""
统一的 Telemetry 收集器
支持单个或多个设备的数据收集，按时间和设备名称保存到独立文件
"""
import sys
sys.stdout.reconfigure(encoding='utf-8')
import socket
import json
import time
import threading
import os
from datetime import datetime
from collections import defaultdict

class TelemetryCollector:
    """统一的 Telemetry 收集器"""
    
    def __init__(self, host='0.0.0.0', port=57400, data_dir='telemetry_data'):
        self.host = host
        self.port = port
        self.data_dir = data_dir
        self.running = False
        self.server_socket = None
        self.client_connections = []
        self.total_data_count = 0
        self.start_time = None
        
        # 确保数据目录存在
        if not os.path.exists(self.data_dir):
            os.makedirs(self.data_dir)
        
        # 设备统计信息
        self.device_stats = defaultdict(lambda: {
            'message_count': 0,
            'last_seen': None,
            'first_seen': None,
            'data_file': None
        })
        
        # 全局统计
        self.active_devices = set()
    
    def start(self):
        """启动收集器"""
        
        print("🌟 统一 Telemetry 收集器")
        print(f"📡 监听地址: {self.host}:{self.port}")
        print(f"📁 数据目录: {self.data_dir}")
        print(f"⏰ 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("🔍 等待设备连接...")
        print("=" * 80)
        
        try:
            # 创建服务器套接字
            print(f"🔧 创建服务器套接字...")
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)

            print(f"🔗 绑定到 {self.host}:{self.port}...")
            self.server_socket.bind((self.host, self.port))

            print(f"👂 开始监听，队列大小: 20...")
            self.server_socket.listen(200)

            print(f"✅ 服务器套接字创建成功")
            
            self.running = True
            self.start_time = datetime.now()
            
            # 启动状态报告线程
            status_thread = threading.Thread(target=self._status_reporter)
            status_thread.daemon = True
            status_thread.start()
            
            while self.running:
                try:
                    # 接受连接
                    client_socket, client_address = self.server_socket.accept()
                    print(f"\n🔗 新连接: {client_address}")
                    print(f"⏰ 连接时间: {datetime.now().strftime('%H:%M:%S')}")
                    
                    self.client_connections.append(client_socket)
                    
                    # 为每个客户端启动处理线程
                    client_thread = threading.Thread(
                        target=self._handle_client,
                        args=(client_socket, client_address)
                    )
                    client_thread.daemon = True
                    client_thread.start()
                    
                except Exception as e:
                    if self.running:
                        print(f"❌ 接受连接失败: {e}")
                    
        except Exception as e:
            print(f"❌ 启动收集器失败: {e}")
        
        finally:
            try:
                if self.server_socket:
                    self.server_socket.close()
            except:
                pass
    
    def _handle_client(self, client_socket, client_address):
        """处理客户端数据"""
        
        buffer = ""
        
        try:
            while self.running:
                data = client_socket.recv(8192)
                if not data:
                    break
                
                try:
                    decoded_data = data.decode('utf-8')
                    buffer += decoded_data
                    
                    # 处理完整的消息
                    while '\n' in buffer:
                        line, buffer = buffer.split('\n', 1)
                        if line.strip():
                            self._process_message(line.strip(), client_address)
                
                except UnicodeDecodeError:
                    # 处理二进制数据
                    self._process_binary_message(data, client_address)
        
        except Exception as e:
            print(f"❌ 处理客户端 {client_address} 数据失败: {e}")
        
        finally:
            try:
                client_socket.close()
                if client_socket in self.client_connections:
                    self.client_connections.remove(client_socket)
                print(f"🔌 客户端 {client_address} 断开连接")
            except:
                pass
    
    def _process_message(self, data, client_address):
        """处理文本消息"""
        
        self.total_data_count += 1
        timestamp = datetime.now()
        
        # 尝试解析 JSON
        try:
            json_data = json.loads(data)
            device_name = json_data.get('device', f'unknown-{client_address[0]}')
            
            # 更新设备统计
            self._update_device_stats(device_name, timestamp)
            
            # 显示消息
            self._display_message(device_name, json_data, timestamp, client_address)
            
            # 保存数据到设备专用文件
            self._save_device_data(device_name, data, timestamp, client_address)
            
        except json.JSONDecodeError:
            device_name = f'unknown-{client_address[0]}'
            print(f"\n📝 [{timestamp.strftime('%H:%M:%S')}] 原始数据来自 {device_name}")
            print(f"   数据: {data[:100]}...")
            
            # 保存原始数据
            self._save_device_data(device_name, data, timestamp, client_address)
    
    def _update_device_stats(self, device_name, timestamp):
        """更新设备统计信息"""
        
        stats = self.device_stats[device_name]
        stats['message_count'] += 1
        stats['last_seen'] = timestamp
        
        if stats['first_seen'] is None:
            stats['first_seen'] = timestamp
            # 为新设备创建数据文件路径
            date_str = timestamp.strftime('%Y%m%d')
            stats['data_file'] = os.path.join(
                self.data_dir, 
                f"{device_name}_{date_str}.json"
            )
        
        # 更新活跃设备集合
        self.active_devices.add(device_name)
    
    def _display_message(self, device_name, data, timestamp, client_address):
        """显示消息"""
        
        device_count = self.device_stats[device_name]['message_count']
        timestamp_str = timestamp.strftime('%H:%M:%S.%f')[:-3]
        
        print(f"\n📊 [{timestamp_str}] 设备: {device_name} | 消息 #{device_count} | 来自: {client_address}")
        print("=" * 80)
        
        # 显示基本信息
        if 'sensor_group' in data:
            print(f"📡 传感器组: {data['sensor_group']}")
        
        if 'subscription' in data:
            print(f"📋 订阅: {data['subscription']}")
        
        if 'sequence' in data:
            print(f"🔢 序列号: {data['sequence']}")
        
        if 'timestamp' in data:
            print(f"⏰ 设备时间戳: {data['timestamp']}")
        
        print("start#######################")
        print(data)
        print("end###########################")
        time.sleep(5000)
        # 提取并显示关键信息
        key_info = self._extract_key_info(data)
        if key_info:
            for info_type, info_value in key_info.items():
                print(f"{info_type}: {info_value}")
        
        # 显示数据大小
        data_size = len(json.dumps(data))
        print(f"📏 数据大小: {data_size} 字节")
        
        print("-" * 60)
    
    def _extract_key_info(self, data):
        """提取关键信息"""
        
        key_info = {}
        
        try:
            # 查找 CPU 利用率
            cpu_info = self._find_nested_info(data, ['cpu', 'utilization', 'instant'])
            if cpu_info:
                key_info['💻 CPU 利用率'] = f"{cpu_info}%"
            
            # 查找光功率
            power_info = self._find_nested_info(data, ['output-power', 'output_power'])
            if power_info:
                key_info['🔌 输出功率'] = f"{power_info} dBm"
            
            # 查找温度
            temp_info = self._find_nested_info(data, ['temperature'])
            if temp_info:
                key_info['🌡️ 温度'] = f"{temp_info} °C"
            
            # 查找频率
            freq_info = self._find_nested_info(data, ['frequency'])
            if freq_info:
                key_info['📡 频率'] = f"{freq_info} MHz"
            
            # 统计组件数量
            component_count = self._count_components(data)
            if component_count:
                key_info['🔧 组件'] = component_count
        
        except Exception as e:
            print(f"⚠️ 信息提取失败: {e}")
        
        return key_info
    
    def _find_nested_info(self, obj, keywords):
        """在嵌套对象中查找包含关键词的值"""
        
        def search(obj, path=""):
            if isinstance(obj, dict):
                for key, value in obj.items():
                    current_path = f"{path}.{key}" if path else key
                    
                    # 检查键名是否包含关键词
                    for keyword in keywords:
                        if keyword.lower() in key.lower():
                            if isinstance(value, (int, float)):
                                return value
                            elif isinstance(value, dict) and 'instant' in value:
                                return value['instant']
                    
                    # 递归搜索
                    if isinstance(value, (dict, list)):
                        result = search(value, current_path)
                        if result is not None:
                            return result
            
            elif isinstance(obj, list):
                for i, item in enumerate(obj):
                    result = search(item, f"{path}[{i}]")
                    if result is not None:
                        return result
            
            return None
        
        return search(obj)
    
    def _count_components(self, data):
        """统计组件数量"""
        
        counts = {}
        
        def count_items(obj, path=""):
            if isinstance(obj, dict):
                for key, value in obj.items():
                    # 统计特定组件
                    if 'transceiver' in key.lower():
                        counts['收发器'] = counts.get('收发器', 0) + 1
                    elif 'optical-channel' in key.lower():
                        counts['光通道'] = counts.get('光通道', 0) + 1
                    elif 'logical-channel' in key.lower():
                        counts['逻辑通道'] = counts.get('逻辑通道', 0) + 1
                    elif 'cpu' in key.lower():
                        counts['CPU'] = counts.get('CPU', 0) + 1
                    
                    if isinstance(value, (dict, list)):
                        count_items(value, f"{path}.{key}")
            
            elif isinstance(obj, list):
                for item in obj:
                    count_items(item, path)
        
        count_items(data)
        
        if counts:
            return ", ".join([f"{k}: {v}" for k, v in counts.items()])
        return None
    
    def _save_device_data(self, device_name, data, timestamp, client_address):
        """保存设备数据到专用文件"""
        
        try:
            stats = self.device_stats[device_name]
            data_file = stats['data_file']
            
            # 如果文件路径不存在，创建新的
            if not data_file:
                date_str = timestamp.strftime('%Y%m%d')
                data_file = os.path.join(
                    self.data_dir, 
                    f"{device_name}_{date_str}.json"
                )
                stats['data_file'] = data_file
            
            # 准备数据记录
            record = {
                'timestamp': timestamp.isoformat(),
                'device': device_name,
                'client_address': f"{client_address[0]}:{client_address[1]}",
                'message_count': stats['message_count'],
                'data': data
            }
            
            # 追加到文件
            with open(data_file, 'a', encoding='utf-8') as f:
                f.write(json.dumps(record, ensure_ascii=False) + '\n')
        
        except Exception as e:
            print(f"❌ 保存设备 {device_name} 数据失败: {e}")
    
    def _process_binary_message(self, data, client_address):
        """处理二进制消息"""
        
        self.total_data_count += 1
        timestamp = datetime.now()
        
        print(f"\n📊 [{timestamp.strftime('%H:%M:%S')}] 二进制数据来自 {client_address}")
        print(f"📏 数据长度: {len(data)} 字节")
        
        # 保存二进制数据
        device_name = f'binary-{client_address[0]}'
        self._save_binary_data(device_name, data, timestamp, client_address)
    
    def _save_binary_data(self, device_name, data, timestamp, client_address):
        """保存二进制数据"""
        
        try:
            date_str = timestamp.strftime('%Y%m%d')
            filename = os.path.join(
                self.data_dir, 
                f"{device_name}_{date_str}.bin"
            )
            
            with open(filename, 'ab') as f:
                # 写入时间戳和数据长度头部
                header = f"{timestamp.isoformat()}|{len(data)}|".encode('utf-8')
                f.write(header)
                f.write(data)
                f.write(b'\n')
        
        except Exception as e:
            print(f"❌ 保存二进制数据失败: {e}")
    
    def _status_reporter(self):
        """状态报告线程"""
        while self.running:
            time.sleep(30)  # 每30秒报告一次
            if self.running and self.start_time:
                self._print_status_report()
    
    def _print_status_report(self):
        """打印状态报告"""
        runtime = datetime.now() - self.start_time
        
        print(f"\n📊 === Telemetry 收集器状态报告 ===")
        print(f"⏱️  运行时间: {runtime}")
        print(f"📨 总消息数: {self.total_data_count}")
        print(f"🔗 活跃连接: {len(self.client_connections)}")
        print(f"📱 活跃设备: {len(self.active_devices)}")
        
        # 按设备显示统计
        if self.active_devices:
            print(f"\n📋 设备详情:")
            for device_name in sorted(self.active_devices):
                stats = self.device_stats[device_name]
                last_seen = stats['last_seen']
                time_since = datetime.now() - last_seen if last_seen else None
                
                if time_since and time_since.total_seconds() < 60:
                    status = "🟢 活跃"
                elif time_since and time_since.total_seconds() < 300:
                    status = "🟡 空闲"
                else:
                    status = "🔴 离线"
                
                data_file = os.path.basename(stats['data_file']) if stats['data_file'] else 'N/A'
                
                print(f"   {device_name}: {status} | 消息: {stats['message_count']} | 文件: {data_file}")
                print(f"     首次: {stats['first_seen'].strftime('%H:%M:%S') if stats['first_seen'] else 'N/A'} | "
                      f"最后: {last_seen.strftime('%H:%M:%S') if last_seen else 'N/A'}")
        
        print("=" * 50)
    
    def stop(self):
        """停止收集器"""
        self.running = False
        
        for client in self.client_connections:
            try:
                client.close()
            except:
                pass
        self.client_connections.clear()
        
        if self.server_socket:
            try:
                self.server_socket.close()
            except:
                pass
        
        print("🛑 Telemetry 收集器已停止")
        
        # 显示最终统计
        print(f"\n📊 === 最终统计 ===")
        print(f"📨 总消息数: {self.total_data_count}")
        print(f"📱 设备数量: {len(self.active_devices)}")
        
        if self.active_devices:
            print(f"📁 生成的数据文件:")
            for device_name in sorted(self.active_devices):
                stats = self.device_stats[device_name]
                if stats['data_file']:
                    print(f"   {device_name}: {stats['data_file']} ({stats['message_count']} 条消息)")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='统一 Telemetry 收集器')
    parser.add_argument('--host', default='0.0.0.0', help='监听地址')
    parser.add_argument('--port', type=int, default=57400, help='监听端口')
    parser.add_argument('--data-dir', default='telemetry_data', help='数据保存目录')
    
    args = parser.parse_args()
    
    collector = TelemetryCollector(args.host, args.port, args.data_dir)
    
    try:
        collector.start()
    except KeyboardInterrupt:
        print("\n收到停止信号...")
        collector.stop()
    except Exception as e:
        print(f"收集器运行失败: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    import sys
    sys.exit(main())
