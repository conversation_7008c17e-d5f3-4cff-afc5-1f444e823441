# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

import telemetry_service_pb2 as telemetry__service__pb2

GRPC_GENERATED_VERSION = '1.74.0'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in telemetry_service_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class TelemetryServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.telemetrySubscribe = channel.unary_stream(
                '/telemetry.TelemetryService/telemetrySubscribe',
                request_serializer=telemetry__service__pb2.TelemetrySubscriptionRequest.SerializeToString,
                response_deserializer=telemetry__service__pb2.TelemetryUpdate.FromString,
                _registered_method=True)


class TelemetryServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def telemetrySubscribe(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_TelemetryServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'telemetrySubscribe': grpc.unary_stream_rpc_method_handler(
                    servicer.telemetrySubscribe,
                    request_deserializer=telemetry__service__pb2.TelemetrySubscriptionRequest.FromString,
                    response_serializer=telemetry__service__pb2.TelemetryUpdate.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'telemetry.TelemetryService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('telemetry.TelemetryService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class TelemetryService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def telemetrySubscribe(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_stream(
            request,
            target,
            '/telemetry.TelemetryService/telemetrySubscribe',
            telemetry__service__pb2.TelemetrySubscriptionRequest.SerializeToString,
            telemetry__service__pb2.TelemetryUpdate.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
