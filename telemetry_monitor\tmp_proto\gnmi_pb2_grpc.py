# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from tmp_proto import gnmi_pb2 as tmp__proto_dot_gnmi__pb2

GRPC_GENERATED_VERSION = '1.74.0'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in tmp_proto/gnmi_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class gNMIStub(object):
    """The gNMI service definition.
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.Subscribe = channel.stream_stream(
                '/gnmi.gNMI/Subscribe',
                request_serializer=tmp__proto_dot_gnmi__pb2.SubscribeRequest.SerializeToString,
                response_deserializer=tmp__proto_dot_gnmi__pb2.SubscribeResponse.FromString,
                _registered_method=True)
        self.Publish = channel.stream_unary(
                '/gnmi.gNMI/Publish',
                request_serializer=tmp__proto_dot_gnmi__pb2.SubscribeResponse.SerializeToString,
                response_deserializer=tmp__proto_dot_gnmi__pb2.PublishResponse.FromString,
                _registered_method=True)


class gNMIServicer(object):
    """The gNMI service definition.
    """

    def Subscribe(self, request_iterator, context):
        """Subscribe allows a client to subscribe to a stream of notifications from
        a target. The client sends a SubscribeRequest, and the target sends
        a stream of SubscribeResponse messages.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def Publish(self, request_iterator, context):
        """Publish is a streaming RPC that allows a client to stream telemetry
        notifications to a target. A single Publish RPC can be used to stream
        notifications for multiple subscriptions.
        NOTE: This RPC is typically used for targets to publish data to collectors,
        but the original proto had it defined this way. For a collector,
        the Subscribe RPC is the primary one.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_gNMIServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'Subscribe': grpc.stream_stream_rpc_method_handler(
                    servicer.Subscribe,
                    request_deserializer=tmp__proto_dot_gnmi__pb2.SubscribeRequest.FromString,
                    response_serializer=tmp__proto_dot_gnmi__pb2.SubscribeResponse.SerializeToString,
            ),
            'Publish': grpc.stream_unary_rpc_method_handler(
                    servicer.Publish,
                    request_deserializer=tmp__proto_dot_gnmi__pb2.SubscribeResponse.FromString,
                    response_serializer=tmp__proto_dot_gnmi__pb2.PublishResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'gnmi.gNMI', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('gnmi.gNMI', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class gNMI(object):
    """The gNMI service definition.
    """

    @staticmethod
    def Subscribe(request_iterator,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.stream_stream(
            request_iterator,
            target,
            '/gnmi.gNMI/Subscribe',
            tmp__proto_dot_gnmi__pb2.SubscribeRequest.SerializeToString,
            tmp__proto_dot_gnmi__pb2.SubscribeResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def Publish(request_iterator,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.stream_unary(
            request_iterator,
            target,
            '/gnmi.gNMI/Publish',
            tmp__proto_dot_gnmi__pb2.SubscribeResponse.SerializeToString,
            tmp__proto_dot_gnmi__pb2.PublishResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
