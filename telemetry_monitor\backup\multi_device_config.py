#!/usr/bin/env python3
"""
多设备 Telemetry 配置管理器
支持同时配置和管理多个设备的 telemetry 订阅
"""

import sys
import os
import threading
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(__file__))

from src.netconf_client import NetconfClient
from src.utils import setup_logging

class MultiDeviceManager:
    """多设备管理器"""
    
    def __init__(self, device_config_file="config/device_config.yaml"):
        self.client = NetconfClient(device_config_file)
        self.results = {}
        self.lock = threading.Lock()
        
        # 完整的 Telemetry 配置模板
        self.telemetry_config_template = '''
<config xmlns="urn:ietf:params:xml:ns:netconf:base:1.0">
  <telemetry-system xmlns="http://openconfig.net/yang/telemetry">
    <sensor-groups>
      <sensor-group>
        <sensor-group-id>allSg</sensor-group-id>
        <config>
          <sensor-group-id>allSg</sensor-group-id>
        </config>
        <sensor-paths>
          <sensor-path>
            <path>/openconfig-platform:components/component/openconfig-platform-transceiver:transceiver/state</path>
            <config>
              <path>/openconfig-platform:components/component/openconfig-platform-transceiver:transceiver/state</path>
            </config>
          </sensor-path>
          <sensor-path>
            <path>/openconfig-platform:components/component/state</path>
            <config>
              <path>/openconfig-platform:components/component/state</path>
            </config>
          </sensor-path>
          <sensor-path>
            <path>/openconfig-platform:components/component/openconfig-terminal-device:optical-channel/state</path>
            <config>
              <path>/openconfig-platform:components/component/openconfig-terminal-device:optical-channel/state</path>
            </config>
          </sensor-path>
          <sensor-path>
            <path>/openconfig-terminal-device:terminal-device/logical-channels/channel/otn/state</path>
            <config>
              <path>/openconfig-terminal-device:terminal-device/logical-channels/channel/otn/state</path>
            </config>
          </sensor-path>
          <sensor-path>
            <path>/openconfig-terminal-device:terminal-device/logical-channels/channel/ethernet/state</path>
            <config>
              <path>/openconfig-terminal-device:terminal-device/logical-channels/channel/ethernet/state</path>
            </config>
          </sensor-path>
          <sensor-path>
            <path>/openconfig-platform:components/component/cpu/openconfig-platform-cpu:utilization</path>
            <config>
              <path>/openconfig-platform:components/component/cpu/openconfig-platform-cpu:utilization</path>
            </config>
          </sensor-path>
          <sensor-path>
            <path>/openconfig-platform:components/component/openconfig-platform-transceiver:transceiver/physical-channels/channel/state</path>
            <config>
              <path>/openconfig-platform:components/component/openconfig-platform-transceiver:transceiver/physical-channels/channel/state</path>
            </config>
          </sensor-path>
        </sensor-paths>
      </sensor-group>
    </sensor-groups>
    
    <destination-groups>
      <destination-group>
        <group-id>optical-collector</group-id>
        <config>
          <group-id>optical-collector</group-id>
        </config>
        <destinations>
          <destination>
            <destination-address>***************</destination-address>
            <destination-port>57400</destination-port>
            <config>
              <destination-address>***************</destination-address>
              <destination-port>57400</destination-port>
            </config>
          </destination>
        </destinations>
      </destination-group>
    </destination-groups>
  </telemetry-system>
</config>
'''
    
    def get_available_devices(self):
        """获取可用设备列表"""
        return list(self.client.devices.keys())
    
    def test_device_connection(self, device_name):
        """测试单个设备连接"""
        try:
            result = self.client.validate_connection(device_name)
            with self.lock:
                self.results[device_name] = {
                    'connection': 'success' if result else 'failed',
                    'timestamp': datetime.now(),
                    'error': None
                }
            return result
        except Exception as e:
            with self.lock:
                self.results[device_name] = {
                    'connection': 'error',
                    'timestamp': datetime.now(),
                    'error': str(e)
                }
            return False
    
    def configure_device_telemetry(self, device_name):
        """配置单个设备的 telemetry"""
        try:
            print(f"🔧 [{datetime.now().strftime('%H:%M:%S')}] 开始配置设备: {device_name}")
            
            # 测试连接
            if not self.client.validate_connection(device_name):
                raise Exception("设备连接失败")
            
            # 应用配置
            with self.client.get_connection(device_name) as conn:
                result = conn.edit_config(target='running', config=self.telemetry_config_template)
                
            with self.lock:
                self.results[device_name] = {
                    'connection': 'success',
                    'configuration': 'success',
                    'timestamp': datetime.now(),
                    'error': None
                }
            
            print(f"✅ [{datetime.now().strftime('%H:%M:%S')}] 设备 {device_name} 配置成功")
            return True
            
        except Exception as e:
            with self.lock:
                self.results[device_name] = {
                    'connection': 'unknown',
                    'configuration': 'failed',
                    'timestamp': datetime.now(),
                    'error': str(e)
                }
            
            print(f"❌ [{datetime.now().strftime('%H:%M:%S')}] 设备 {device_name} 配置失败: {e}")
            return False
    
    def check_device_status(self, device_name):
        """检查单个设备的 telemetry 状态"""
        try:
            with self.client.get_connection(device_name) as conn:
                filter_xml = '''
                <telemetry-system xmlns="http://openconfig.net/yang/telemetry">
                  <sensor-groups/>
                  <destination-groups/>
                </telemetry-system>
                '''
                
                result = conn.get_config(source='running', filter=('subtree', filter_xml))
                config_str = str(result)
                
                status = {
                    'sensor_groups': 'allSg' in config_str,
                    'destination_groups': 'optical-collector' in config_str,
                    'timestamp': datetime.now(),
                    'error': None
                }
                
                with self.lock:
                    if device_name not in self.results:
                        self.results[device_name] = {}
                    self.results[device_name].update(status)
                
                return status
                
        except Exception as e:
            status = {
                'sensor_groups': False,
                'destination_groups': False,
                'timestamp': datetime.now(),
                'error': str(e)
            }
            
            with self.lock:
                if device_name not in self.results:
                    self.results[device_name] = {}
                self.results[device_name].update(status)
            
            return status
    
    def batch_test_connections(self, device_names=None, max_workers=5):
        """批量测试设备连接"""
        if device_names is None:
            device_names = self.get_available_devices()
        
        print(f"🔍 开始批量测试 {len(device_names)} 个设备的连接...")
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_device = {
                executor.submit(self.test_device_connection, device): device 
                for device in device_names
            }
            
            for future in as_completed(future_to_device):
                device = future_to_device[future]
                try:
                    result = future.result()
                    status = "✅ 成功" if result else "❌ 失败"
                    print(f"   {device}: {status}")
                except Exception as e:
                    print(f"   {device}: ❌ 错误 - {e}")
        
        return self.results
    
    def batch_configure_telemetry(self, device_names=None, max_workers=3):
        """批量配置设备 telemetry"""
        if device_names is None:
            device_names = self.get_available_devices()
        
        print(f"⚙️ 开始批量配置 {len(device_names)} 个设备的 telemetry...")
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_device = {
                executor.submit(self.configure_device_telemetry, device): device 
                for device in device_names
            }
            
            for future in as_completed(future_to_device):
                device = future_to_device[future]
                try:
                    future.result()
                except Exception as e:
                    print(f"❌ 设备 {device} 配置过程中出现异常: {e}")
        
        return self.results
    
    def batch_check_status(self, device_names=None, max_workers=5):
        """批量检查设备状态"""
        if device_names is None:
            device_names = self.get_available_devices()
        
        print(f"📊 开始批量检查 {len(device_names)} 个设备的状态...")
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_device = {
                executor.submit(self.check_device_status, device): device 
                for device in device_names
            }
            
            for future in as_completed(future_to_device):
                device = future_to_device[future]
                try:
                    status = future.result()
                    sensor_status = "✅" if status['sensor_groups'] else "❌"
                    dest_status = "✅" if status['destination_groups'] else "❌"
                    print(f"   {device}: 传感器组 {sensor_status}, 目标组 {dest_status}")
                except Exception as e:
                    print(f"   {device}: ❌ 检查失败 - {e}")
        
        return self.results
    
    def print_summary(self):
        """打印结果摘要"""
        print("\n📋 === 多设备操作结果摘要 ===")
        
        if not self.results:
            print("   没有操作结果")
            return
        
        for device_name, result in self.results.items():
            print(f"\n🔧 设备: {device_name}")
            
            if 'connection' in result:
                conn_status = {
                    'success': '✅ 连接成功',
                    'failed': '❌ 连接失败', 
                    'error': '⚠️ 连接错误'
                }.get(result['connection'], '❓ 未知状态')
                print(f"   连接状态: {conn_status}")
            
            if 'configuration' in result:
                config_status = {
                    'success': '✅ 配置成功',
                    'failed': '❌ 配置失败'
                }.get(result['configuration'], '❓ 未知状态')
                print(f"   配置状态: {config_status}")
            
            if 'sensor_groups' in result:
                sensor_status = "✅ 已配置" if result['sensor_groups'] else "❌ 未配置"
                dest_status = "✅ 已配置" if result['destination_groups'] else "❌ 未配置"
                print(f"   传感器组: {sensor_status}")
                print(f"   目标组: {dest_status}")
            
            if result.get('error'):
                print(f"   错误信息: {result['error']}")
            
            if 'timestamp' in result:
                print(f"   最后更新: {result['timestamp'].strftime('%Y-%m-%d %H:%M:%S')}")
    
    def save_results(self, filename=None):
        """保存结果到文件"""
        if filename is None:
            filename = f"multi_device_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        import json
        
        # 转换 datetime 对象为字符串
        serializable_results = {}
        for device, result in self.results.items():
            serializable_results[device] = {}
            for key, value in result.items():
                if isinstance(value, datetime):
                    serializable_results[device][key] = value.isoformat()
                else:
                    serializable_results[device][key] = value
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(serializable_results, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 结果已保存到: {filename}")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='多设备 Telemetry 配置管理')
    parser.add_argument('action', choices=['test', 'configure', 'check', 'all'], 
                       help='操作类型: test=测试连接, configure=配置telemetry, check=检查状态, all=全部操作')
    parser.add_argument('--devices', nargs='+', help='指定设备名称（默认为所有设备）')
    parser.add_argument('--workers', type=int, default=3, help='并发工作线程数（默认3）')
    parser.add_argument('--save', action='store_true', help='保存结果到文件')
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logging("INFO")
    
    # 创建管理器
    manager = MultiDeviceManager()
    
    print("🚀 多设备 Telemetry 配置管理器")
    print("=" * 60)
    
    # 显示可用设备
    available_devices = manager.get_available_devices()
    print(f"📱 可用设备: {', '.join(available_devices)}")
    
    # 确定要操作的设备
    target_devices = args.devices if args.devices else available_devices
    print(f"🎯 目标设备: {', '.join(target_devices)}")
    print()
    
    try:
        if args.action in ['test', 'all']:
            manager.batch_test_connections(target_devices, args.workers)
        
        if args.action in ['configure', 'all']:
            manager.batch_configure_telemetry(target_devices, args.workers)
        
        if args.action in ['check', 'all']:
            manager.batch_check_status(target_devices, args.workers)
        
        # 显示摘要
        manager.print_summary()
        
        # 保存结果
        if args.save:
            manager.save_results()
        
        return 0
        
    except KeyboardInterrupt:
        print("\n⚠️ 操作被用户中断")
        return 1
    except Exception as e:
        print(f"❌ 操作失败: {e}")
        return 1
    finally:
        try:
            manager.client.disconnect_all()
        except:
            pass

if __name__ == "__main__":
    sys.exit(main())
