#!/usr/bin/env python3
"""
遥测数据查看工具
用于查看和分析结构化的遥测数据
"""

import json
import os
import glob
from datetime import datetime
import argparse

def list_data_files():
    """列出所有可用的数据文件"""
    data_dir = "telemetry_data"
    if not os.path.exists(data_dir):
        print(f"❌ 数据目录不存在: {data_dir}")
        return []
    
    # 查找结构化数据文件
    structured_files = glob.glob(os.path.join(data_dir, "*_structured_*.json"))
    latest_files = glob.glob(os.path.join(data_dir, "*_latest_structured.json"))
    
    print(f"📁 数据目录: {data_dir}")
    print(f"📊 结构化数据文件: {len(structured_files)} 个")
    print(f"🔄 最新数据文件: {len(latest_files)} 个")
    
    all_files = structured_files + latest_files
    all_files.sort()
    
    return all_files

def view_latest_data(device_ip=None):
    """查看最新的遥测数据"""
    data_dir = "telemetry_data"
    
    if device_ip:
        latest_file = os.path.join(data_dir, f"{device_ip}_latest_structured.json")
        if not os.path.exists(latest_file):
            print(f"❌ 设备 {device_ip} 的最新数据文件不存在")
            return
        files_to_check = [latest_file]
    else:
        files_to_check = glob.glob(os.path.join(data_dir, "*_latest_structured.json"))
    
    if not files_to_check:
        print("❌ 没有找到最新数据文件")
        return
    
    for file_path in files_to_check:
        print(f"\n📄 文件: {os.path.basename(file_path)}")
        print("=" * 60)
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 显示基本信息
            print(f"🕒 时间戳: {data.get('timestamp', 'N/A')}")
            print(f"🖥️  设备ID: {data.get('device_id', 'N/A')}")
            print(f"📡 订阅: {data.get('subscription', 'N/A')}")
            
            # 显示sensor paths数据
            sensor_paths = data.get('sensor_paths', {})
            print(f"📊 Sensor Paths: {len(sensor_paths)} 个")
            
            for path, path_data in sensor_paths.items():
                print(f"\n  🔗 {path}")
                if isinstance(path_data, dict):
                    for key, value in path_data.items():
                        if isinstance(value, dict):
                            print(f"    📋 {key}:")
                            for sub_key, sub_value in value.items():
                                print(f"      - {sub_key}: {sub_value}")
                        else:
                            print(f"    - {key}: {value}")
                else:
                    print(f"    值: {path_data}")
                    
        except json.JSONDecodeError as e:
            print(f"❌ JSON解析错误: {e}")
        except Exception as e:
            print(f"❌ 读取文件错误: {e}")

def view_historical_data(device_ip, date=None):
    """查看历史遥测数据"""
    data_dir = "telemetry_data"
    
    if date:
        file_pattern = f"{device_ip}_structured_{date}.json"
    else:
        file_pattern = f"{device_ip}_structured_*.json"
    
    files = glob.glob(os.path.join(data_dir, file_pattern))
    files.sort()
    
    if not files:
        print(f"❌ 没有找到设备 {device_ip} 的历史数据文件")
        return
    
    print(f"📅 设备 {device_ip} 的历史数据文件:")
    for i, file_path in enumerate(files):
        file_name = os.path.basename(file_path)
        file_size = os.path.getsize(file_path)
        file_time = datetime.fromtimestamp(os.path.getmtime(file_path))
        print(f"  {i+1}. {file_name} ({file_size} bytes, {file_time.strftime('%Y-%m-%d %H:%M:%S')})")
    
    if len(files) == 1:
        selected_file = files[0]
    else:
        try:
            choice = input(f"\n请选择文件 (1-{len(files)}, 回车选择最新): ").strip()
            if not choice:
                selected_file = files[-1]  # 最新文件
            else:
                selected_file = files[int(choice) - 1]
        except (ValueError, IndexError):
            print("❌ 无效选择")
            return
    
    print(f"\n📄 查看文件: {os.path.basename(selected_file)}")
    print("=" * 60)
    
    try:
        with open(selected_file, 'r', encoding='utf-8') as f:
            # 读取文件中的所有JSON记录
            records = []
            for line in f:
                line = line.strip()
                if line:
                    try:
                        record = json.loads(line)
                        records.append(record)
                    except json.JSONDecodeError:
                        continue
        
        print(f"📊 找到 {len(records)} 条记录")
        
        if records:
            # 显示最新记录
            latest_record = records[-1]
            print(f"\n🔄 最新记录:")
            print(f"🕒 时间戳: {latest_record.get('timestamp', 'N/A')}")
            print(f"📡 订阅: {latest_record.get('subscription', 'N/A')}")
            
            sensor_paths = latest_record.get('sensor_paths', {})
            print(f"📊 Sensor Paths: {len(sensor_paths)} 个")
            for path in sensor_paths.keys():
                print(f"  - {path}")
                
    except Exception as e:
        print(f"❌ 读取文件错误: {e}")

def analyze_data_trends(device_ip):
    """分析数据趋势"""
    data_dir = "telemetry_data"
    files = glob.glob(os.path.join(data_dir, f"{device_ip}_structured_*.json"))
    
    if not files:
        print(f"❌ 没有找到设备 {device_ip} 的数据文件")
        return
    
    print(f"📈 分析设备 {device_ip} 的数据趋势...")
    
    all_records = []
    for file_path in files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line:
                        try:
                            record = json.loads(line)
                            all_records.append(record)
                        except json.JSONDecodeError:
                            continue
        except Exception as e:
            print(f"⚠️ 读取文件 {file_path} 失败: {e}")
    
    if not all_records:
        print("❌ 没有找到有效的数据记录")
        return
    
    print(f"📊 总共找到 {len(all_records)} 条记录")
    
    # 分析时间范围
    timestamps = [record.get('timestamp') for record in all_records if record.get('timestamp')]
    if timestamps:
        print(f"⏰ 时间范围: {min(timestamps)} 到 {max(timestamps)}")
    
    # 分析sensor paths
    all_paths = set()
    for record in all_records:
        sensor_paths = record.get('sensor_paths', {})
        all_paths.update(sensor_paths.keys())
    
    print(f"🔗 发现的Sensor Paths ({len(all_paths)} 个):")
    for path in sorted(all_paths):
        print(f"  - {path}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="遥测数据查看工具")
    parser.add_argument("--list", action="store_true", help="列出所有数据文件")
    parser.add_argument("--latest", help="查看最新数据 (可指定设备IP)")
    parser.add_argument("--history", help="查看历史数据 (指定设备IP)")
    parser.add_argument("--date", help="指定日期 (格式: YYYYMMDD)")
    parser.add_argument("--analyze", help="分析数据趋势 (指定设备IP)")
    
    args = parser.parse_args()
    
    if args.list:
        files = list_data_files()
        if files:
            print("\n📋 可用文件:")
            for file_path in files:
                print(f"  - {os.path.basename(file_path)}")
    
    elif args.latest is not None:
        device_ip = args.latest if args.latest else None
        view_latest_data(device_ip)
    
    elif args.history:
        view_historical_data(args.history, args.date)
    
    elif args.analyze:
        analyze_data_trends(args.analyze)
    
    else:
        # 默认显示最新数据
        print("🔍 显示所有设备的最新数据...")
        view_latest_data()

if __name__ == "__main__":
    main()
