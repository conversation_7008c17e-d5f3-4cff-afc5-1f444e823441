#!/usr/bin/env python3
"""
测试运行器
运行所有单元测试并生成测试报告
"""

import unittest
import sys
import os
import time
from io import StringIO

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(__file__))

from src.utils import setup_logging


class ColoredTextTestResult(unittest.TextTestResult):
    """带颜色的测试结果输出"""
    
    def __init__(self, stream, descriptions, verbosity):
        super().__init__(stream, descriptions, verbosity)
        self.success_count = 0
    
    def addSuccess(self, test):
        super().addSuccess(test)
        self.success_count += 1
        if self.verbosity > 1:
            self.stream.write(f"\033[92m✓ {test._testMethodName}\033[0m\n")
        else:
            self.stream.write('\033[92m.\033[0m')
        self.stream.flush()
    
    def addError(self, test, err):
        super().addError(test, err)
        if self.verbosity > 1:
            self.stream.write(f"\033[91m✗ {test._testMethodName} (ERROR)\033[0m\n")
        else:
            self.stream.write('\033[91mE\033[0m')
        self.stream.flush()
    
    def addFailure(self, test, err):
        super().addFailure(test, err)
        if self.verbosity > 1:
            self.stream.write(f"\033[91m✗ {test._testMethodName} (FAIL)\033[0m\n")
        else:
            self.stream.write('\033[91mF\033[0m')
        self.stream.flush()
    
    def addSkip(self, test, reason):
        super().addSkip(test, reason)
        if self.verbosity > 1:
            self.stream.write(f"\033[93m- {test._testMethodName} (SKIP: {reason})\033[0m\n")
        else:
            self.stream.write('\033[93ms\033[0m')
        self.stream.flush()


class ColoredTextTestRunner(unittest.TextTestRunner):
    """带颜色的测试运行器"""
    
    resultclass = ColoredTextTestResult
    
    def run(self, test):
        """运行测试并返回结果"""
        result = super().run(test)
        
        # 打印测试总结
        print("\n" + "="*70)
        print("测试总结:")
        print(f"运行测试: {result.testsRun}")
        print(f"\033[92m成功: {result.success_count}\033[0m")
        print(f"\033[91m失败: {len(result.failures)}\033[0m")
        print(f"\033[91m错误: {len(result.errors)}\033[0m")
        print(f"\033[93m跳过: {len(result.skipped)}\033[0m")
        
        if result.failures:
            print(f"\n\033[91m失败的测试:\033[0m")
            for test, traceback in result.failures:
                print(f"  - {test}")
        
        if result.errors:
            print(f"\n\033[91m错误的测试:\033[0m")
            for test, traceback in result.errors:
                print(f"  - {test}")
        
        success_rate = (result.success_count / result.testsRun * 100) if result.testsRun > 0 else 0
        print(f"\n成功率: {success_rate:.1f}%")
        
        return result


def discover_tests(test_dir='tests', pattern='test_*.py'):
    """发现测试用例"""
    loader = unittest.TestLoader()
    suite = loader.discover(test_dir, pattern=pattern)
    return suite


def run_specific_test(test_module, test_class=None, test_method=None):
    """运行特定的测试"""
    if test_method and test_class:
        suite = unittest.TestSuite()
        suite.addTest(test_class(test_method))
    elif test_class:
        suite = unittest.TestLoader().loadTestsFromTestCase(test_class)
    else:
        suite = unittest.TestLoader().loadTestsFromModule(test_module)
    
    return suite


def generate_test_report(result, output_file='test_report.txt'):
    """生成测试报告"""
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("OpenConfig Telemetry Monitor 测试报告\n")
        f.write("="*50 + "\n")
        f.write(f"生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        f.write("测试统计:\n")
        f.write(f"  总测试数: {result.testsRun}\n")
        f.write(f"  成功: {result.success_count}\n")
        f.write(f"  失败: {len(result.failures)}\n")
        f.write(f"  错误: {len(result.errors)}\n")
        f.write(f"  跳过: {len(result.skipped)}\n")
        
        success_rate = (result.success_count / result.testsRun * 100) if result.testsRun > 0 else 0
        f.write(f"  成功率: {success_rate:.1f}%\n\n")
        
        if result.failures:
            f.write("失败的测试:\n")
            for i, (test, traceback) in enumerate(result.failures, 1):
                f.write(f"{i}. {test}\n")
                f.write(f"   错误信息:\n")
                f.write(f"   {traceback}\n\n")
        
        if result.errors:
            f.write("错误的测试:\n")
            for i, (test, traceback) in enumerate(result.errors, 1):
                f.write(f"{i}. {test}\n")
                f.write(f"   错误信息:\n")
                f.write(f"   {traceback}\n\n")
        
        if result.skipped:
            f.write("跳过的测试:\n")
            for i, (test, reason) in enumerate(result.skipped, 1):
                f.write(f"{i}. {test}\n")
                f.write(f"   跳过原因: {reason}\n\n")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='OpenConfig Telemetry Monitor 测试运行器')
    parser.add_argument('--verbose', '-v', action='count', default=1,
                       help='详细输出级别 (使用 -v, -vv 增加详细程度)')
    parser.add_argument('--pattern', '-p', default='test_*.py',
                       help='测试文件匹配模式')
    parser.add_argument('--directory', '-d', default='tests',
                       help='测试目录')
    parser.add_argument('--module', '-m', 
                       help='运行特定模块的测试 (例如: test_yang_templates)')
    parser.add_argument('--class', '-c', dest='test_class',
                       help='运行特定类的测试')
    parser.add_argument('--method', dest='test_method',
                       help='运行特定方法的测试')
    parser.add_argument('--report', '-r', action='store_true',
                       help='生成测试报告文件')
    parser.add_argument('--log-level', default='WARNING',
                       choices=['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'],
                       help='日志级别')
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logging(args.log_level)
    
    print("OpenConfig Telemetry Monitor 测试运行器")
    print("="*50)
    
    # 发现或选择测试
    if args.module:
        try:
            # 导入指定模块
            module_name = f"tests.{args.module}" if not args.module.startswith('tests.') else args.module
            module = __import__(module_name, fromlist=[''])
            
            if args.test_class:
                test_class = getattr(module, args.test_class)
                suite = run_specific_test(module, test_class, args.test_method)
            else:
                suite = run_specific_test(module)
                
            print(f"运行模块: {args.module}")
            if args.test_class:
                print(f"测试类: {args.test_class}")
            if args.test_method:
                print(f"测试方法: {args.test_method}")
                
        except ImportError as e:
            print(f"错误: 无法导入模块 '{args.module}': {e}")
            return 1
        except AttributeError as e:
            print(f"错误: 无法找到测试类 '{args.test_class}': {e}")
            return 1
    else:
        print(f"发现测试: {args.directory}/{args.pattern}")
        suite = discover_tests(args.directory, args.pattern)
    
    # 运行测试
    runner = ColoredTextTestRunner(verbosity=args.verbose)
    start_time = time.time()
    result = runner.run(suite)
    end_time = time.time()
    
    print(f"\n运行时间: {end_time - start_time:.2f} 秒")
    
    # 生成报告
    if args.report:
        report_file = 'test_report.txt'
        generate_test_report(result, report_file)
        print(f"测试报告已生成: {report_file}")
    
    # 返回适当的退出码
    if result.failures or result.errors:
        return 1
    return 0


if __name__ == '__main__':
    sys.exit(main())