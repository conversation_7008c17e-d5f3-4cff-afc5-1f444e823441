import grpc

class TelemetryGpbTable:
    def __init__(self):
        self.row = []

class Row:
    def __init__(self):
        self.timestamp = 0
        self.keys = {}
        self.content = {}

class TelemetryData:
    def __init__(self):
        self.encoding_path = ""
        self.node_id_str = ""
        self.subscription_id_str = ""
        self.collection_id = 0
        self.collection_start_time = 0
        self.msg_timestamp = 0
        self.data_gpbkv = []
        self.data_gpb = TelemetryGpbTable()
