#!/usr/bin/env python3
"""
快速连接测试脚本
用于快速验证 Nokia ALU 设备连接
"""

import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(__file__))

from src.utils import setup_logging

def test_basic_connection():
    """测试基本连接功能"""
    print("=== 快速连接测试 ===\n")
    
    # 设置日志
    setup_logging("INFO")
    
    try:
        # 测试导入
        print("1. 测试模块导入...")
        from src.netconf_client import NetconfClient
        print("✓ NetconfClient 导入成功")
        
        from src.telemetry_config import TelemetryConfigurator
        print("✓ TelemetryConfigurator 导入成功")
        
        # 初始化客户端
        print("\n2. 初始化 NETCONF 客户端...")
        client = NetconfClient("config/device_config.yaml")
        print("✓ NETCONF 客户端初始化成功")
        
        # 列出设备
        print("\n3. 列出配置的设备...")
        devices = client.list_devices()
        print(f"找到 {len(devices)} 个设备:")
        for device in devices:
            info = client.get_device_info(device)
            print(f"  - {device}: {info['host']}:{info['port']} (类型: {info['device_type']})")
        
        # 测试 Nokia 设备连接
        nokia_device = "nokia-alu-1"
        if nokia_device in devices:
            print(f"\n4. 测试 Nokia 设备连接...")
            print(f"正在连接到 {nokia_device}...")
            
            try:
                # 尝试连接
                with client.get_connection(nokia_device) as conn:
                    print("✓ 连接建立成功!")
                    
                    # 获取服务器能力
                    capabilities = list(conn.server_capabilities)
                    print(f"✓ 获取到 {len(capabilities)} 个服务器能力")
                    
                    # 显示一些关键能力
                    print("\n关键能力:")
                    for cap in capabilities[:5]:
                        print(f"  - {cap}")
                    
                    if len(capabilities) > 5:
                        print(f"  ... 还有 {len(capabilities) - 5} 个能力")
                    
                    # 检查 OpenConfig 支持
                    openconfig_caps = [cap for cap in capabilities if 'openconfig' in cap.lower()]
                    if openconfig_caps:
                        print(f"\n✓ 发现 {len(openconfig_caps)} 个 OpenConfig 能力")
                    else:
                        print("\n⚠ 未发现 OpenConfig 能力")
                    
                    print("\n✓ 连接测试成功!")
                    return True
                    
            except Exception as e:
                print(f"✗ 连接失败: {e}")
                print("\n可能的原因:")
                print("1. 网络连接问题")
                print("2. 设备 NETCONF 服务未启用")
                print("3. 认证信息错误")
                print("4. 防火墙阻止连接")
                return False
        else:
            print(f"\n✗ 未找到设备 '{nokia_device}'")
            return False
            
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        try:
            client.disconnect_all()
        except:
            pass


def main():
    """主函数"""
    success = test_basic_connection()
    
    if success:
        print("\n=== 测试结果 ===")
        print("✓ 基本连接测试通过")
        print("\n下一步:")
        print("1. 运行完整测试: python test_nokia_device.py")
        print("2. 使用命令行工具: python main.py test-connection nokia-alu-1")
        return 0
    else:
        print("\n=== 测试结果 ===")
        print("✗ 连接测试失败")
        print("\n故障排查:")
        print("1. 检查网络连接: ping 100.120.162.113")
        print("2. 检查 NETCONF 端口: telnet 100.120.162.113 830")
        print("3. 验证设备配置文件: config/device_config.yaml")
        print("4. 检查设备 NETCONF 服务状态")
        return 1


if __name__ == "__main__":
    sys.exit(main())