#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实时监控设备telemetry数据并记录到JSON文件
每个设备使用单独的线程和文件
"""

import json
import time
import threading
import logging
import socket
import struct
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional
import grpc
from google.protobuf.message import Message
import xml.etree.ElementTree as ET

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(threadName)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class TelemetryConfig:
    """Telemetry配置类"""
    def __init__(self):
        # 从XML配置中提取的信息
        self.collector_ip = "***************"
        self.collector_port = 57400
        #self.device_ip = "***************" 
        self.device_ip = "***************" 
        #self.subscription_name = "allPsg"
        #self.sensor_group = "allSg"

        self.subscription_name = "Cluster_86_sensor_20250806100102"
        self.sensor_group = "Cluster_86_sensor_20250806100102"

        #self.sample_interval = 1000  # 毫秒
        self.sample_interval = 5000  # 毫秒
        self.heartbeat_interval = 15000  # 毫秒
        
        # 监控的路径
        self.sensor_paths = [
            "/openconfig-terminal-device:terminal-device/logical-channels/channel/otn/state",
            "/openconfig-terminal-device:terminal-device/logical-channels/channel/ethernet/state",
            "/openconfig-platform:components/component/openconfig-platform-transceiver:transceiver/physical-channels/channel/state",
            "/openconfig-platform:components/component/state", 
            "/openconfig-platform:components/component/cpu/openconfig-platform-cpu:utilization",
            "/openconfig-platform:components/component/openconfig-terminal-device:optical-channel/state",
            "/openconfig-platform:components/component/openconfig-platform-transceiver:transceiver/state",
            "/openconfig-platform:components/component/openconfig-transport-line-common:optical-port/state"
        ]

class DeviceMonitor:
    """单个设备监控器"""
    
    def __init__(self, device_id: str, config: TelemetryConfig, output_dir: str = "telemetry_data"):
        self.device_id = device_id
        self.config = config
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # 为每个设备创建单独的JSON文件
        tmp_datatime = datetime.now().strftime('%Y%m%d%H%M%S')
        self.json_file = self.output_dir / f"{device_id}_telemetry_{tmp_datatime}.json"
        self.running = False
        self.thread = None
        self.data_buffer = []
        self.buffer_lock = threading.Lock()
        
    def start_monitoring(self):
        """启动监控线程"""
        if self.running:
            logger.warning(f"设备 {self.device_id} 监控已在运行")
            return
            
        self.running = True
        self.thread = threading.Thread(
            target=self._monitor_loop,
            name=f"Monitor-{self.device_id}"
        )
        self.thread.daemon = True
        self.thread.start()
        logger.info(f"启动设备 {self.device_id} 的监控线程")
        
    def stop_monitoring(self):
        """停止监控"""
        self.running = False
        if self.thread and self.thread.is_alive():
            self.thread.join(timeout=5)
        logger.info(f"停止设备 {self.device_id} 的监控")
        
    def _monitor_loop(self):
        """主监控循环"""
        logger.info(f"设备 {self.device_id} 监控循环开始")
        
        while self.running:
            try:
                # 模拟接收telemetry数据
                telemetry_data = self._receive_telemetry_data()
                
                if telemetry_data:
                    self._process_telemetry_data(telemetry_data)
                    
                # 定期写入文件
                self._write_buffer_to_file()
                
                # 根据采样间隔休眠
                time.sleep(self.config.sample_interval / 1000.0)
                
            except Exception as e:
                logger.error(f"设备 {self.device_id} 监控出错: {e}")
                time.sleep(1)
                
        logger.info(f"设备 {self.device_id} 监控循环结束")
        
    def _receive_telemetry_data(self) -> Optional[Dict[str, Any]]:
        """接收telemetry数据 (模拟实现)"""
        # 这里应该实现实际的gRPC客户端来接收数据
        # 目前创建模拟数据用于演示
        
        current_time = datetime.now()
        
        # 模拟不同路径的数据
        simulated_data = {
            "timestamp": current_time.isoformat(),
            "device_id": self.device_id,
            "subscription": self.config.subscription_name,
            "sensor_paths": {}
        }
        
        # 为每个传感器路径生成模拟数据
        import random
        for path in self.config.sensor_paths:
            if "ethernet/state" in path:
                simulated_data["sensor_paths"][path] = {
                    "counters": {
                        "in-octets": random.randint(1000000, 9999999),
                        "out-octets": random.randint(1000000, 9999999),
                        "in-pkts": random.randint(1000, 9999),
                        "out-pkts": random.randint(1000, 9999)
                    }
                }
            elif "component/state" in path:
                simulated_data["sensor_paths"][path] = {
                    "temperature": {
                        "instant": round(random.uniform(25.0, 45.0), 2),
                        "avg": round(random.uniform(30.0, 40.0), 2),
                        "max": round(random.uniform(40.0, 50.0), 2)
                    },
                    "memory": {
                        "utilized": random.randint(1024, 8192),
                        "available": random.randint(8192, 16384)
                    }
                }
            elif "cpu/utilization" in path:
                simulated_data["sensor_paths"][path] = {
                    "instant": round(random.uniform(10.0, 80.0), 2),
                    "avg": round(random.uniform(20.0, 60.0), 2),
                    "max": round(random.uniform(60.0, 90.0), 2)
                }
            elif "transceiver" in path:
                simulated_data["sensor_paths"][path] = {
                    "output-power": round(random.uniform(-10.0, 5.0), 2),
                    "input-power": round(random.uniform(-15.0, 0.0), 2),
                    "laser-bias-current": round(random.uniform(20.0, 80.0), 2),
                    "temperature": round(random.uniform(20.0, 70.0), 2)
                }
            elif "optical-channel" in path:
                simulated_data["sensor_paths"][path] = {
                    "frequency": random.randint(191000000, 196000000),
                    "target-output-power": round(random.uniform(-5.0, 5.0), 2),
                    "operational-mode": random.randint(1, 10)
                }
                
        return simulated_data
        
    def _process_telemetry_data(self, data: Dict[str, Any]):
        """处理telemetry数据"""
        with self.buffer_lock:
            self.data_buffer.append(data)
            
        # 如果缓冲区太大，立即写入
        if len(self.data_buffer) >= 100:
            self._write_buffer_to_file()
            
    def _write_buffer_to_file(self):
        """将缓冲区数据写入JSON文件"""
        if not self.data_buffer:
            return
            
        with self.buffer_lock:
            data_to_write = self.data_buffer.copy()
            self.data_buffer.clear()
            
        try:
            # 读取现有数据
            existing_data = []
            if self.json_file.exists():
                with open(self.json_file, 'r', encoding='utf-8') as f:
                    try:
                        existing_data = json.load(f)
                        if not isinstance(existing_data, list):
                            existing_data = []
                    except json.JSONDecodeError:
                        existing_data = []
                        
            # 添加新数据
            existing_data.extend(data_to_write)
            
            # 保持文件大小合理，只保留最近的1000条记录
            if len(existing_data) > 1000:
                existing_data = existing_data[-1000:]
                
            # 写入文件
            with open(self.json_file, 'w', encoding='utf-8') as f:
                json.dump(existing_data, f, indent=2, ensure_ascii=False)
                
            logger.debug(f"写入 {len(data_to_write)} 条数据到 {self.json_file}")
            
        except Exception as e:
            logger.error(f"写入文件失败 {self.json_file}: {e}")

class TelemetryMonitorManager:
    """Telemetry监控管理器"""
    
    def __init__(self, config: TelemetryConfig):
        self.config = config
        self.device_monitors: Dict[str, DeviceMonitor] = {}
        self.running = False
        
    def add_device(self, device_id: str):
        """添加设备监控"""
        if device_id in self.device_monitors:
            logger.warning(f"设备 {device_id} 已存在")
            return
            
        monitor = DeviceMonitor(device_id, self.config)
        self.device_monitors[device_id] = monitor
        
        if self.running:
            monitor.start_monitoring()
            
        logger.info(f"添加设备 {device_id} 到监控列表")
        
    def remove_device(self, device_id: str):
        """移除设备监控"""
        if device_id not in self.device_monitors:
            logger.warning(f"设备 {device_id} 不存在")
            return
            
        monitor = self.device_monitors[device_id]
        monitor.stop_monitoring()
        del self.device_monitors[device_id]
        
        logger.info(f"从监控列表移除设备 {device_id}")
        
    def start_all_monitoring(self):
        """启动所有设备监控"""
        if self.running:
            logger.warning("监控已在运行")
            return
            
        self.running = True
        
        for device_id, monitor in self.device_monitors.items():
            monitor.start_monitoring()
            
        logger.info(f"启动所有设备监控，共 {len(self.device_monitors)} 个设备")
        
    def stop_all_monitoring(self):
        """停止所有设备监控"""
        self.running = False
        
        for device_id, monitor in self.device_monitors.items():
            monitor.stop_monitoring()
            
        logger.info("停止所有设备监控")
        
    def get_status(self) -> Dict[str, Any]:
        """获取监控状态"""
        status = {
            "running": self.running,
            "device_count": len(self.device_monitors),
            "devices": {}
        }
        
        for device_id, monitor in self.device_monitors.items():
            status["devices"][device_id] = {
                "running": monitor.running,
                "json_file": str(monitor.json_file),
                "buffer_size": len(monitor.data_buffer)
            }
            
        return status

def main():
    """主函数"""
    # 创建配置
    config = TelemetryConfig()
    
    # 创建监控管理器
    manager = TelemetryMonitorManager(config)
    
    # 添加设备 (这里使用配置中的设备IP作为设备ID)
    device_list = [
        config.device_ip,  # 主设备

    ]


    # device_list = [
    #     config.device_ip,  # 主设备
    #     "***************",  # 可以添加更多设备
    #     "***************"
    # ]
    
    for device_id in device_list:
        manager.add_device(device_id)
    
    try:
        # 启动监控
        manager.start_all_monitoring()
        
        print("Telemetry监控已启动...")
        print("按 Ctrl+C 停止监控")
        
        # 定期打印状态
        while True:
            time.sleep(30)  # 每30秒打印一次状态
            status = manager.get_status()
            print(f"\n=== 监控状态 ===")
            print(f"运行状态: {status['running']}")
            print(f"设备数量: {status['device_count']}")
            
            for device_id, device_status in status["devices"].items():
                print(f"设备 {device_id}: 运行={device_status['running']}, "
                      f"缓冲区={device_status['buffer_size']}, "
                      f"文件={device_status['json_file']}")
                      
    except KeyboardInterrupt:
        print("\n收到停止信号...")
        manager.stop_all_monitoring()
        print("监控已停止")

if __name__ == "__main__":
    main()
