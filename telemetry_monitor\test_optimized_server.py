#!/usr/bin/env python3
"""
测试优化后的遥测服务器
"""

import json
import os
from datetime import datetime

def test_data_processing():
    """测试数据处理函数"""
    
    # 导入优化后的函数
    from telemetry_grpc_server import (
        create_structured_telemetry_data,
        process_ethernet_state,
        process_transceiver_physical_channel_state,
        process_component_state,
        normalize_sensor_path
    )
    
    print("🧪 测试数据处理函数...")
    
    # 测试路径标准化
    test_paths = [
        "/openconfig-terminal-device:terminal-device/logical-channels/channel/ethernet/state",
        "/openconfig-platform:components/component/state",
        "/openconfig-platform:components/component/openconfig-platform-transceiver:transceiver/physical-channels/channel/state"
    ]
    
    print("\n📍 测试路径标准化:")
    for path in test_paths:
        normalized = normalize_sensor_path(path)
        print(f"  原始: {path}")
        print(f"  标准化: {normalized}")
        print()
    
    # 测试以太网数据处理
    print("🌐 测试以太网数据处理:")
    ethernet_data = {
        "counters": {
            "in-octets": "6164784",
            "out-octets": "4168902", 
            "in-pkts": "9468",
            "out-pkts": "7604"
        }
    }
    processed_ethernet = process_ethernet_state(ethernet_data)
    print(f"  处理结果: {json.dumps(processed_ethernet, indent=2)}")
    
    # 测试收发器数据处理
    print("\n📡 测试收发器数据处理:")
    transceiver_data = {
        "output-power": "-2.52",
        "input-power": "-14.41",
        "laser-bias-current": "29.89",
        "temperature": "34.86"
    }
    processed_transceiver = process_transceiver_physical_channel_state(transceiver_data)
    print(f"  处理结果: {json.dumps(processed_transceiver, indent=2)}")
    
    # 测试组件状态数据处理
    print("\n🔧 测试组件状态数据处理:")
    component_data = {
        "temperature": {
            "instant": "28.23",
            "avg": "31.06", 
            "max": "40.07"
        },
        "memory": {
            "utilized": "5880",
            "available": "14998"
        }
    }
    processed_component = process_component_state(component_data)
    print(f"  处理结果: {json.dumps(processed_component, indent=2)}")
    
    # 测试结构化数据创建
    print("\n📊 测试结构化数据创建:")
    device_ip = "***************"
    subscription_name = "allPsg"
    
    updates_data = {
        "/openconfig-terminal-device:terminal-device/logical-channels/channel/ethernet/state": ethernet_data,
        "/openconfig-platform:components/component/openconfig-platform-transceiver:transceiver/physical-channels/channel/state": transceiver_data,
        "/openconfig-platform:components/component/state": component_data
    }
    
    structured_data = create_structured_telemetry_data(device_ip, subscription_name, updates_data)
    print(f"  结构化数据: {json.dumps(structured_data, indent=2, ensure_ascii=False)}")
    
    return structured_data

def test_json_output_format():
    """测试JSON输出格式是否符合要求"""
    print("\n✅ 测试JSON输出格式...")
    
    structured_data = test_data_processing()
    
    # 验证必需字段
    required_fields = ["timestamp", "device_id", "subscription", "sensor_paths"]
    for field in required_fields:
        if field not in structured_data:
            print(f"❌ 缺少必需字段: {field}")
            return False
        else:
            print(f"✅ 包含字段: {field}")
    
    # 验证sensor_paths结构
    sensor_paths = structured_data.get("sensor_paths", {})
    if not isinstance(sensor_paths, dict):
        print(f"❌ sensor_paths 应该是字典类型")
        return False
    
    print(f"✅ sensor_paths 包含 {len(sensor_paths)} 个路径")
    for path, data in sensor_paths.items():
        print(f"  - {path}: {type(data)}")
    
    # 保存测试结果到文件
    test_output_file = "test_structured_output.json"
    with open(test_output_file, 'w', encoding='utf-8') as f:
        json.dump(structured_data, f, indent=2, ensure_ascii=False)
    
    print(f"✅ 测试结果已保存到: {test_output_file}")
    return True

def main():
    """主测试函数"""
    print("🚀 开始测试优化后的遥测服务器...")
    print("=" * 60)
    
    try:
        # 测试数据处理
        success = test_json_output_format()
        
        if success:
            print("\n🎉 所有测试通过!")
            print("优化后的代码应该能够:")
            print("  ✅ 正确解析所有sensor path数据")
            print("  ✅ 生成符合要求的JSON格式")
            print("  ✅ 处理各种数据类型")
            print("  ✅ 标准化路径名称")
        else:
            print("\n❌ 测试失败，请检查代码")
            
    except Exception as e:
        print(f"\n❌ 测试过程中出现异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
