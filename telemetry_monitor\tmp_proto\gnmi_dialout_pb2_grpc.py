# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from tmp_proto import gnmi_base_pb2 as tmp__proto_dot_gnmi__base__pb2

GRPC_GENERATED_VERSION = '1.74.0'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in tmp_proto/gnmi_dialout_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class gNMIDialoutStub(object):
    """The gNMI Dialout service definition for Nokia devices.
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.Publish = channel.stream_unary(
                '/gnmi_dialout.gNMIDialout/Publish',
                request_serializer=tmp__proto_dot_gnmi__base__pb2.SubscribeResponse.SerializeToString,
                response_deserializer=tmp__proto_dot_gnmi__base__pb2.PublishResponse.FromString,
                _registered_method=True)


class gNMIDialoutServicer(object):
    """The gNMI Dialout service definition for Nokia devices.
    """

    def Publish(self, request_iterator, context):
        """Publish is a streaming RPC that allows a target to stream telemetry
        notifications to a collector.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_gNMIDialoutServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'Publish': grpc.stream_unary_rpc_method_handler(
                    servicer.Publish,
                    request_deserializer=tmp__proto_dot_gnmi__base__pb2.SubscribeResponse.FromString,
                    response_serializer=tmp__proto_dot_gnmi__base__pb2.PublishResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'gnmi_dialout.gNMIDialout', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('gnmi_dialout.gNMIDialout', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class gNMIDialout(object):
    """The gNMI Dialout service definition for Nokia devices.
    """

    @staticmethod
    def Publish(request_iterator,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.stream_unary(
            request_iterator,
            target,
            '/gnmi_dialout.gNMIDialout/Publish',
            tmp__proto_dot_gnmi__base__pb2.SubscribeResponse.SerializeToString,
            tmp__proto_dot_gnmi__base__pb2.PublishResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
