#!/usr/bin/env python3
"""
简单的 Telemetry 数据收集器
用于接收和显示来自网络设备的 telemetry 数据
"""

import socket
import json
import threading
import time
from datetime import datetime
import logging

class SimpleTelemetryCollector:
    """简单的 Telemetry 收集器"""
    
    def __init__(self, host='0.0.0.0', port=57400):
        """
        初始化收集器
        
        Args:
            host: 监听地址
            port: 监听端口
        """
        self.host = host
        self.port = port
        self.running = False
        self.server_socket = None
        self.clients = []
        self.data_count = 0
        
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
    
    def start(self):
        """启动收集器"""
        try:
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.server_socket.bind((self.host, self.port))
            self.server_socket.listen(5)
            
            self.running = True
            self.logger.info(f"Telemetry 收集器启动，监听 {self.host}:{self.port}")
            
            # 启动接受连接的线程
            accept_thread = threading.Thread(target=self._accept_connections)
            accept_thread.daemon = True
            accept_thread.start()
            
            # 启动状态报告线程
            status_thread = threading.Thread(target=self._status_reporter)
            status_thread.daemon = True
            status_thread.start()
            
            return True
            
        except Exception as e:
            self.logger.error(f"启动收集器失败: {e}")
            return False
    
    def stop(self):
        """停止收集器"""
        self.running = False
        
        # 关闭所有客户端连接
        for client in self.clients:
            try:
                client.close()
            except:
                pass
        self.clients.clear()
        
        # 关闭服务器套接字
        if self.server_socket:
            try:
                self.server_socket.close()
            except:
                pass
        
        self.logger.info("Telemetry 收集器已停止")
    
    def _accept_connections(self):
        """接受客户端连接"""
        while self.running:
            try:
                client_socket, client_address = self.server_socket.accept()
                self.logger.info(f"新的连接来自: {client_address}")
                
                self.clients.append(client_socket)
                
                # 为每个客户端启动处理线程
                client_thread = threading.Thread(
                    target=self._handle_client,
                    args=(client_socket, client_address)
                )
                client_thread.daemon = True
                client_thread.start()
                
            except Exception as e:
                if self.running:
                    self.logger.error(f"接受连接失败: {e}")
                break
    
    def _handle_client(self, client_socket, client_address):
        """处理客户端数据"""
        buffer = ""
        
        try:
            while self.running:
                data = client_socket.recv(4096)
                if not data:
                    break
                
                # 解码数据
                try:
                    decoded_data = data.decode('utf-8')
                    buffer += decoded_data
                    
                    # 处理完整的消息（假设以换行符分隔）
                    while '\n' in buffer:
                        line, buffer = buffer.split('\n', 1)
                        if line.strip():
                            self._process_telemetry_data(line.strip(), client_address)
                            
                except UnicodeDecodeError:
                    # 如果是二进制数据，直接处理
                    self._process_binary_data(data, client_address)
                
        except Exception as e:
            self.logger.error(f"处理客户端 {client_address} 数据失败: {e}")
        
        finally:
            try:
                client_socket.close()
                if client_socket in self.clients:
                    self.clients.remove(client_socket)
                self.logger.info(f"客户端 {client_address} 断开连接")
            except:
                pass
    
    def _process_telemetry_data(self, data, client_address):
        """处理 telemetry 数据"""
        self.data_count += 1
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        self.logger.info(f"[{timestamp}] 收到数据 #{self.data_count} 来自 {client_address}")
        
        # 尝试解析 JSON 数据
        try:
            json_data = json.loads(data)
            self.logger.info(f"JSON 数据: {json.dumps(json_data, indent=2, ensure_ascii=False)}")
        except json.JSONDecodeError:
            # 如果不是 JSON，直接显示原始数据
            self.logger.info(f"原始数据: {data}")
        
        # 保存数据到文件（可选）
        self._save_data_to_file(data, timestamp, client_address)
    
    def _process_binary_data(self, data, client_address):
        """处理二进制数据"""
        self.data_count += 1
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        self.logger.info(f"[{timestamp}] 收到二进制数据 #{self.data_count} 来自 {client_address}")
        self.logger.info(f"数据长度: {len(data)} 字节")
        self.logger.info(f"数据预览: {data[:100]}...")  # 显示前100字节
    
    def _save_data_to_file(self, data, timestamp, client_address):
        """保存数据到文件"""
        try:
            filename = f"telemetry_data_{datetime.now().strftime('%Y%m%d')}.log"
            with open(filename, 'a', encoding='utf-8') as f:
                f.write(f"[{timestamp}] {client_address}: {data}\n")
        except Exception as e:
            self.logger.error(f"保存数据到文件失败: {e}")
    
    def _status_reporter(self):
        """状态报告线程"""
        while self.running:
            time.sleep(30)  # 每30秒报告一次状态
            if self.running:
                self.logger.info(f"状态报告 - 活跃连接: {len(self.clients)}, 已接收数据: {self.data_count}")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='简单的 Telemetry 数据收集器')
    parser.add_argument('--host', default='0.0.0.0', help='监听地址 (默认: 0.0.0.0)')
    parser.add_argument('--port', type=int, default=57400, help='监听端口 (默认: 57400)')
    
    args = parser.parse_args()
    
    # 创建并启动收集器
    collector = SimpleTelemetryCollector(args.host, args.port)
    
    if collector.start():
        print(f"Telemetry 收集器已启动，监听 {args.host}:{args.port}")
        print("按 Ctrl+C 停止收集器")
        
        try:
            # 保持主线程运行
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n正在停止收集器...")
            collector.stop()
            print("收集器已停止")
    else:
        print("启动收集器失败")
        return 1
    
    return 0


if __name__ == "__main__":
    import sys
    sys.exit(main())
